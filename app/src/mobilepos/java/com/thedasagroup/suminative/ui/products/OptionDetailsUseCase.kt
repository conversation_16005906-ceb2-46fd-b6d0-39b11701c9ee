package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.option_details.GetOptionDetailsRequest
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.OptionRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import kotlinx.coroutines.flow.StateFlow

class OptionDetailsUseCase(
    private val optionRepository: OptionRepository,
    private val stockRepository: StockRepository,
    val prefs: Prefs
) {
    suspend operator fun invoke(itemId: Int): StateFlow<Async<OptionDetails>> {
        return if(prefs.storeConfigurations?.data?.offlinePOS == true){
            // Get option details from local database instead of API
            optionRepository.getOptionDetailsFromDatabase(itemId)
        } else stockRepository.getOptionDetails(request = GetOptionDetailsRequest(itemId = itemId))

    }
}