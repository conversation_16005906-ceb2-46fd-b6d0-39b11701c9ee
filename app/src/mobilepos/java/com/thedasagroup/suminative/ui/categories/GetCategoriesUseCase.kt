package com.thedasagroup.suminative.ui.categories

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import com.thedasagroup.suminative.data.model.response.category_sorting.CategorySortingResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import kotlinx.coroutines.flow.StateFlow

open class GetCategoriesUseCase(
    private val stockRepository: StockRepository,
    private val prefs: Prefs
) {
    suspend operator fun invoke(): StateFlow<Async<CategorySortingResponse>> {

        val storeId = prefs.store?.id?.toString() ?: "0"
        
        return stockRepository.getCategorySorting(
            CategorySortingRequest(storeId = storeId)
        )
    }
} 