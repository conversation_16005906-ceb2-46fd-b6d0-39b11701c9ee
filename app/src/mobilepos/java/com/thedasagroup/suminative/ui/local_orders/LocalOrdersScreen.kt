package com.thedasagroup.suminative.ui.local_orders

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CloudDone
import androidx.compose.material.icons.filled.CloudOff
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.CloudSync
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksViewModel
import com.thedasagroup.suminative.database.OrderEntity
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LocalOrdersScreen(
    viewModel: LocalOrdersViewModel = mavericksViewModel(),
    onBackClick: (() -> Unit)? = null
) {
    val state by viewModel.collectAsState()
    val configuration = LocalConfiguration.current
    val isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
    
    // Mobile-optimized padding
    val horizontalPadding = if (isLandscape) 24.dp else 16.dp
    val verticalPadding = 12.dp
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF8F9FA))
            .padding(horizontal = horizontalPadding, vertical = verticalPadding)
    ) {
        // Mobile-optimized Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Back Button
                onBackClick?.let { backHandler ->
                    IconButton(
                        onClick = backHandler,
                        modifier = Modifier.size(44.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = Color(0xFF2D3748),
                            modifier = Modifier.size(24.dp)
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))
                }
                
                Text(
                    text = "Local Orders",
                    fontSize = if (isLandscape) 22.sp else 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF1A202C)
                )
            }
            
            // Sync All Button
            SyncAllButton(
                viewModel = viewModel,
                state = state
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Mobile-optimized Filter Chips
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            contentPadding = PaddingValues(horizontal = 4.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(OrderFilter.values()) { filter ->
                MobileFilterChip(
                    filter = filter,
                    isSelected = state.selectedFilter == filter,
                    onClick = {
                        when (filter) {
                            OrderFilter.ALL -> viewModel.loadAllOrders()
                            OrderFilter.PENDING -> viewModel.loadOrdersByStatus("PENDING")
                            OrderFilter.SYNCED -> {
                                viewModel.loadSyncedOrders()
                            }
                            OrderFilter.UNSYNCED -> {
                                viewModel.loadUnsyncedOrders()
                            }
                        }
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Orders List
        when (val ordersAsync = state.orders) {
            is Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF4299E1),
                        strokeWidth = 3.dp
                    )
                }
            }
            is Success -> {
                if (ordersAsync.invoke().isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.padding(24.dp)
                        ) {
                            Text(
                                text = "No Orders Found",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.SemiBold,
                                color = Color(0xFF4A5568)
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Orders will appear here once created",
                                fontSize = 14.sp,
                                color = Color(0xFF718096)
                            )
                        }
                    }
                } else {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                        contentPadding = PaddingValues(bottom = 16.dp),
                        modifier = Modifier.fillMaxSize()
                    ) {
                        items(ordersAsync.invoke()) { order ->
                            MobileOrderCard(
                                order = order,
                                onStatusUpdate = { status ->
//                                    viewModel.updateOrderStatus(order.id, status)
                                },
                                onMarkComplete = {
//                                    viewModel.markOrderComplete(order.id)
                                },
                                onSyncOrder = {
                                    viewModel.syncSingleOrder(order.orderId, state.selectedFilter)
                                },
                                isSyncing = state.syncingOrderIds.contains(order.orderId)
                            )
                        }
                    }
                }
            }
            else -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.padding(24.dp)
                    ) {
                        Text(
                            text = "Failed to load orders",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFFE53E3E)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Please try refreshing",
                            fontSize = 14.sp,
                            color = Color(0xFF718096)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MobileFilterChip(
    filter: OrderFilter,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val haptic = LocalHapticFeedback.current
    val backgroundColor = if (isSelected) Color(0xFF4299E1) else Color.White
    val textColor = if (isSelected) Color.White else Color(0xFF2D3748)
    val borderColor = if (isSelected) Color(0xFF4299E1) else Color(0xFFE2E8F0)
    
    Surface(
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .clickable { 
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                onClick() 
            },
        color = backgroundColor,
        border = BorderStroke(1.dp, borderColor),
        shape = RoundedCornerShape(20.dp)
    ) {
        Text(
            text = when (filter) {
                OrderFilter.SYNCED -> "Synced"
                OrderFilter.UNSYNCED -> "Unsynced"
                else -> filter.name.lowercase().replaceFirstChar { it.uppercase() }
            },
            color = textColor,
            fontSize = 13.sp,
            fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Medium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 10.dp)
        )
    }
}

@Composable
fun MobileOrderCard(
    order: OrderEntity,
    onStatusUpdate: (String) -> Unit,
    onMarkComplete: () -> Unit,
    onSyncOrder: () -> Unit,
    isSyncing: Boolean = false
) {
    val dateFormatter = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
    val isSynced = order.synced == 1L
    val haptic = LocalHapticFeedback.current
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp)),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
            pressedElevation = 4.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Mobile-optimized Order Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Order #${order.orderId.take(6)}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF1A202C)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // Compact Sync Status Icon
                        Icon(
                            imageVector = if (isSynced) Icons.Default.CloudDone else Icons.Default.CloudOff,
                            contentDescription = if (isSynced) "Synced" else "Not Synced",
                            tint = if (isSynced) Color(0xFF38A169) else Color(0xFFED8936),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    Text(
                        text = dateFormatter.format(Date(order.createdAt)),
                        fontSize = 12.sp,
                        color = Color(0xFF718096),
                        modifier = Modifier.padding(top = 2.dp)
                    )
                }
                
                MobileOrderStatusChip(status = order.status)
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Compact Customer & Order Info
            Column(
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                if (!order.customerName.isNullOrEmpty()) {
                    Row {
                        Text(
                            text = "Customer: ",
                            fontSize = 13.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color(0xFF718096)
                        )
                        Text(
                            text = order.customerName,
                            fontSize = 13.sp,
                            color = Color(0xFF2D3748),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Type: ${order.orderType}",
                        fontSize = 13.sp,
                        color = Color(0xFF718096)
                    )
                    
                    if (order.tableNumber != null) {
                        Text(
                            text = "Table: ${order.tableNumber}",
                            fontSize = 13.sp,
                            color = Color(0xFF718096)
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Mobile-optimized Total & Actions
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "$${String.format("%.2f", order.total)}",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF1A202C)
                    )
                    Text(
                        text = "${order.paymentStatus} • ${if (isSynced) "Synced" else "Not Synced"}",
                        fontSize = 12.sp,
                        color = if (isSynced) Color(0xFF38A169) else Color(0xFFED8936),
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // Mobile Action Buttons
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Sync Button (only for unsynced orders)
                    if (!isSynced) {
                        FilledTonalIconButton(
                            onClick = { 
                                if (!isSyncing) {
                                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                                    onSyncOrder() 
                                }
                            },
                            enabled = !isSyncing,
                            modifier = Modifier.size(40.dp)
                        ) {
                            if (isSyncing) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(18.dp),
                                    color = Color(0xFF4299E1),
                                    strokeWidth = 2.dp
                                )
                            } else {
                                Icon(
                                    imageVector = Icons.Default.Sync,
                                    contentDescription = "Sync Order",
                                    tint = Color(0xFF4299E1),
                                    modifier = Modifier.size(18.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun MobileOrderStatusChip(status: String) {
    val backgroundColor = when (status) {
        "PENDING" -> Color(0xFFFBB6CE)
        "ACCEPTED" -> Color(0xFF90CDF4)
        "PREPARING" -> Color(0xFFFBD38D)
        "READY" -> Color(0xFFB794F6)
        "COMPLETED" -> Color(0xFF9AE6B4)
        "CANCELLED" -> Color(0xFFFEB2B2)
        else -> Color(0xFFE2E8F0)
    }
    
    val textColor = when (status) {
        "PENDING" -> Color(0xFF97266D)
        "ACCEPTED" -> Color(0xFF2B6CB0)
        "PREPARING" -> Color(0xFFC05621)
        "READY" -> Color(0xFF6B46C1)
        "COMPLETED" -> Color(0xFF276749)
        "CANCELLED" -> Color(0xFFC53030)
        else -> Color(0xFF4A5568)
    }
    
    Surface(
        modifier = Modifier.clip(RoundedCornerShape(12.dp)),
        color = backgroundColor,
        shape = RoundedCornerShape(12.dp)
    ) {
        Text(
            text = status,
            fontSize = 11.sp,
            fontWeight = FontWeight.Bold,
            color = textColor,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp)
        )
    }
}

@Composable
fun SyncAllButton(
    viewModel: LocalOrdersViewModel,
    state: LocalOrdersState
) {
    val haptic = LocalHapticFeedback.current
    val selectedFilter by viewModel.collectAsState(LocalOrdersState::selectedFilter)
    
    when (val syncState = state.isSync) {
        is Loading -> {
            FilledTonalButton(
                onClick = { },
                enabled = false,
                modifier = Modifier.height(40.dp)
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = Color(0xFF4299E1),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "Syncing...",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        is com.airbnb.mvrx.Success -> {
            val result = syncState.invoke()
            if (result.totalCount > 0) {
                FilledTonalButton(
                    onClick = { 
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        viewModel.syncAllOrders(selectedFilter = selectedFilter)
                    },
                    colors = ButtonDefaults.filledTonalButtonColors(
                        containerColor = Color(0xFF38A169).copy(alpha = 0.1f),
                        contentColor = Color(0xFF38A169)
                    ),
                    modifier = Modifier.height(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CloudDone,
                        contentDescription = "Sync completed",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Synced ${result.syncedCount}/${result.totalCount}",
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            } else {
                FilledTonalButton(
                    onClick = { 
                        haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                        viewModel.syncAllOrders(selectedFilter = selectedFilter)
                    },
                    colors = ButtonDefaults.filledTonalButtonColors(
                        containerColor = Color(0xFF4299E1).copy(alpha = 0.1f),
                        contentColor = Color(0xFF4299E1)
                    ),
                    modifier = Modifier.height(40.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.CloudSync,
                        contentDescription = "Sync All",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Sync All",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        is com.airbnb.mvrx.Fail -> {
            FilledTonalButton(
                onClick = { 
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    viewModel.syncAllOrders(selectedFilter = selectedFilter)
                },
                colors = ButtonDefaults.filledTonalButtonColors(
                    containerColor = Color(0xFFE53E3E).copy(alpha = 0.1f),
                    contentColor = Color(0xFFE53E3E)
                ),
                modifier = Modifier.height(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Sync,
                    contentDescription = "Retry sync",
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "Retry",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
        else -> {
            FilledTonalButton(
                onClick = { 
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                    viewModel.syncAllOrders(selectedFilter = selectedFilter)
                },
                colors = ButtonDefaults.filledTonalButtonColors(
                    containerColor = Color(0xFF4299E1).copy(alpha = 0.1f),
                    contentColor = Color(0xFF4299E1)
                ),
                modifier = Modifier.height(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CloudSync,
                    contentDescription = "Sync All",
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "Sync All",
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
} 