package com.thedasagroup.suminative.ui.payment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CashPaymentActivity : AppCompatActivity(), MavericksView {
    
    val viewModel: PaymentViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val order = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra(ARG_ORDER, Order::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra(ARG_ORDER)
        } ?: Order()
        
        setContent {
            SumiNativeTheme {
                CashPaymentScreen(
                    order = order,
                    onDismiss = { 
                        setResult(RESULT_CANCELED)
                        finish() 
                    },
                    onPaymentComplete = { completedOrder ->
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_ORDER, completedOrder)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    viewModel = viewModel
                )
            }
        }
    }

    override fun invalidate() {
        // Called when the state is updated
    }
    
    companion object {
        private const val ARG_ORDER = "extra_order"
        const val RESULT_ORDER = "result_order"
        
        fun createIntent(context: Context, order: Order): Intent {
            return Intent(context, CashPaymentActivity::class.java).apply {
                putExtra(ARG_ORDER, order)
            }
        }
    }
} 