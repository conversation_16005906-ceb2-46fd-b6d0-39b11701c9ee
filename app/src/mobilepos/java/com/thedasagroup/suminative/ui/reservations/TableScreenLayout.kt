package com.thedasagroup.suminative.ui.reservations

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.Table

@Composable
fun AreaTablesContent(
    area: Area,
    tablesResponse: com.airbnb.mvrx.Async<List<Table>>,
    excludedTableIds: List<Int> = emptyList(),
    onTableSelected: (Table) -> Unit,
    tableOrders: Map<Int, com.thedasagroup.suminative.data.model.request.order.Order> = emptyMap()
) {
    when (tablesResponse) {
        is com.airbnb.mvrx.Loading -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2E7D32)
                )
            }
        }

        is com.airbnb.mvrx.Success -> {
            val allTables = tablesResponse.invoke()
            // Filter out already selected tables
            val availableTables = allTables.filter { table ->
                table.id !in excludedTableIds
            }

            if (availableTables.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = if (allTables.isEmpty()) {
                                "No tables available in ${area.description}"
                            } else {
                                "All tables in ${area.description} are already selected"
                            },
                            fontSize = 18.sp,
                            color = Color.Gray,
                            textAlign = TextAlign.Center
                        )
                        if (allTables.isNotEmpty() && availableTables.isEmpty()) {
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Try selecting a different section",
                                fontSize = 14.sp,
                                color = Color.Gray,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            } else {
                // Use horizontal pager with two pages, each containing 4 tables
                TableHorizontalPager(
                    tables = availableTables,
                    onTableSelected = onTableSelected,
                    tableOrders = tableOrders
                )
            }
        }

        is com.airbnb.mvrx.Fail -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Failed to load tables for ${area.description}",
                        fontSize = 18.sp,
                        color = Color.Red
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Please try again",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
        }

        else -> {
            // Uninitialized state - show loading
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(
                    color = Color(0xFF2E7D32)
                )
            }
        }
    }
}

/**
 * Horizontal pager that displays tables in pages of 4 tables each.
 * Each page shows tables in a 2x2 grid layout with page indicators at the bottom.
 *
 * @param tables List of tables to display
 * @param onTableSelected Callback when a table is selected
 * @param tableOrders Map of table orders for displaying cart totals
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TableHorizontalPager(
    tables: List<Table>,
    onTableSelected: (Table) -> Unit,
    tableOrders: Map<Int, com.thedasagroup.suminative.data.model.request.order.Order> = emptyMap()
) {
    // Split tables into pages of 4 tables each
    val tablesPerPage = 2
    val pages = tables.chunked(tablesPerPage)

    //filter tables with position column 1 to 4 and row 1 to 4
    val firstPageTables = tables.filter { table ->
        val details = table.getTableDetails()
        details?.position?.col in 1..2
    }

    val secondPageTables = tables.filter { table ->
        val details = table.getTableDetails()
        details?.position?.col in 3..4
    }

    val thirdPageTables = tables.filter { table ->
        val details = table.getTableDetails()
        details?.position?.col in 5..6
    }

    val fourthPageTables = tables.filter { table ->
        val details = table.getTableDetails()
        details?.position?.col in 7..8
    }

    if (pages.isEmpty()) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "No tables available",
                fontSize = 18.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
        return
    }

    val pagerState = rememberPagerState(pageCount = { 4 })

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Horizontal Pager
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            contentPadding = PaddingValues(horizontal = 16.dp)
        ) { pageIndex ->

            LazyVerticalGrid(
                columns = GridCells.Fixed(1),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(16.dp),
                modifier = Modifier.fillMaxSize()
            ) {
            when(pageIndex){
                0 -> {
                    item {
                        TableBoxLayout(
                            tables = firstPageTables,
                            onTableSelected = onTableSelected,
                            tableOrders = tableOrders,
                            page = pageIndex
                        )
                    }
                }
                1->{
                    item {
                        TableBoxLayout(
                            tables = secondPageTables,
                            onTableSelected = onTableSelected,
                            tableOrders = tableOrders,
                            page = pageIndex
                        )
                    }
                }
                2 -> {
                    item {
                        TableBoxLayout(
                            tables = thirdPageTables,
                            onTableSelected = onTableSelected,
                            tableOrders = tableOrders,
                            page = pageIndex
                        )
                    }
                }
                3->{
                    item {
                        TableBoxLayout(
                            tables = fourthPageTables,
                            onTableSelected = onTableSelected,
                            tableOrders = tableOrders,
                            page = pageIndex
                        )
                    }
                }
            }
            }
        }

        // Page indicators
        if (firstPageTables.isNotEmpty() && secondPageTables.isNotEmpty()) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                repeat(4) { index ->
                    val isSelected = pagerState.currentPage == index
                    Card(
                        modifier = Modifier
                            .size(12.dp)
                            .padding(horizontal = 4.dp)
                            .clip(CircleShape),
                        colors = CardDefaults.cardColors(
                            containerColor = if (isSelected) Color(0xFF2E7D32) else Color.Gray.copy(
                                alpha = 0.3f
                            )
                        ),
                        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                    ) {}
                }
            }
        }
    }
}

@Composable
fun TableCard(
    table: Table,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    cartTotal: Double = 0.0
) {
    val isOccupied = table.occupied
    val isReserved = table.reserved
    val tableDetails = table.getTableDetails()

    // Parse color from tableDetailsJson or use default
    val customColor = try {
        tableDetails?.color?.let { colorString ->
            if (colorString.startsWith("#")) {
                Color(android.graphics.Color.parseColor(colorString))
            } else {
                Color(0xFF2E7D32) // Default green
            }
        } ?: Color(0xFF2E7D32)
    } catch (e: Exception) {
        Color(0xFF2E7D32) // Default green if parsing fails
    }

    val backgroundColor = when {
        isOccupied -> Color.Red.copy(alpha = 0.7f)
        isReserved -> Color.Yellow.copy(alpha = 0.7f)
        else -> customColor.copy(alpha = 0.1f) // Light version of custom color
    }
    val textColor = when {
        isOccupied || isReserved -> Color.White
        else -> Color.Black
    }
    val borderColor = customColor

    // Determine shape from tableDetailsJson
    val shape = when (tableDetails?.shape?.uppercase()) {
        "CIRCLE" -> CircleShape
        "ROUND" -> CircleShape
        "SQUARE" -> RoundedCornerShape(8.dp)
        "RECTANGLE" -> RoundedCornerShape(12.dp)
        else -> RoundedCornerShape(12.dp) // Default to rectangle
    }

    Card(
        modifier = modifier
            .clickable { onClick() } // Allow clicking on all tables including occupied ones
            .border(
                width = 2.dp,
                color = borderColor,
                shape = shape
            ),
        shape = shape,
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = table.tableName,
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = textColor,
                    textAlign = TextAlign.Center,
                    maxLines = 1
                )
                // Show netPayable amount if table is occupied
                if (isOccupied && table.netPayable != null && table.netPayable!! > 0) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = "$${"%.2f".format(table.netPayable)}",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        textAlign = TextAlign.Center,
                        maxLines = 1
                    )
                }
                // Show cart total if there are items (for non-occupied tables)
                else if (!isOccupied && cartTotal > 0) {
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = "$${"%.2f".format(cartTotal)}",
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        textAlign = TextAlign.Center,
                        maxLines = 1
                    )
                }
            }
        }
    }
}

@Composable
fun TableBoxLayout(
    page : Int,
    tables: List<Table>,
    onTableSelected: (Table) -> Unit,
    tableOrders: Map<Int, com.thedasagroup.suminative.data.model.request.order.Order> = emptyMap()
) {
    // Separate tables with and without position data
    val tablesWithPosition = tables.filter { it.getTableDetails()?.position != null }
    // Use fixed 8x8 grid dimensions (can be overridden by tableDetailsJson)
    val firstTableDetails = tablesWithPosition.firstOrNull()?.getTableDetails()
    val gridRows = 8
    val gridCols = 2

    // page 1 (1-2) 1-2
    // page 2 (3-4) 1-2
    // page 3 (5-6) 1-2
    // page 4 (7-8) 1-2


    // Create a map of position to table for quick lookup
    val tablePositionMap = tablesWithPosition.associateBy { table ->
        val details = table.getTableDetails()
        Pair(details?.position?.row ?: 1,  (details?.position?.col  ?: 1) - (2 * page))
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(600.dp)
            .padding(16.dp)
    ) {
        // Create all rows from 1 to gridRows to show proper spacing
        for (row in 1..gridRows) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(80.dp) // Fixed height for consistent spacing
            ) {
                // Create columns
                for (col in 1..gridCols) {
                    val table = tablePositionMap[Pair(row, col )]
                    if (table != null) {
                        val cartTotal = tableOrders[table.id]?.totalPrice ?: 0.0
                        TableCard(
                            table = table,
                            onClick = { onTableSelected(table) },
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight(),
                            cartTotal = cartTotal
                        )
                    } else {
                        // Empty space to maintain grid structure - no visible grid lines
                        // This ensures proper spacing even for empty rows
                        Spacer(
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        )
                    }
                }
            }

            // Add vertical spacing between rows
            if (row < gridRows) {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}