package com.thedasagroup.suminative.ui.user_profile

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.login.User
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.WaitersRepository
import com.thedasagroup.suminative.ui.login.ClockInUserTimeUseCase
import com.thedasagroup.suminative.ui.login.ClockOutUserTimeUseCase
import com.thedasagroup.suminative.ui.login.LoginUseCase
import com.thedasagroup.suminative.ui.login.StoreUserLoginUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class SelectUserProfileViewModel @AssistedInject constructor(
    @Assisted state: SelectUserProfileState,
    private val waitersRepository: WaitersRepository,
    private val loginUseCase: LoginUseCase,
    private val storeUserLoginUseCase: StoreUserLoginUseCase,
    private val clockInUserTimeUseCase: ClockInUserTimeUseCase,
    private val clockOutUserTimeUseCase: ClockOutUserTimeUseCase,
    val prefs: Prefs
) : MavericksViewModel<SelectUserProfileState>(state) {

    init {
        loadWaiters()
    }

    fun loadWaiters() {
        setState { copy(waitersResponse = Loading()) }
        
        val storeId = prefs.store?.id?.toString() ?: ""
        if (storeId.isNotEmpty()) {
            suspend {
                waitersRepository.getWaitersList(storeId)
            }.execute { response ->
                copy(waitersResponse = response()?.value ?: Uninitialized)
            }
        }
    }

    suspend fun loginWithWaiter(email: String, password: String): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        setState { copy(loginResponse = Loading()) }
        
        loginUseCase(email = email, password = password, shouldEncrypt = false).execute { response ->
            when (response) {
                is Success -> {
                    flow.value = response()
                    copy(loginResponse = response())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(loginResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    fun selectWaiter(waiter: User) {
        setState { copy(selectedWaiter = waiter) }
    }

    fun updatePassword(password: String) {
        setState { copy(password = password) }
    }

    fun updatePasswordError(error: String) {
        setState { copy(passwordError = error) }
    }

    fun updateLoginError(error: String) {
        setState { copy(loginError = error) }
    }
    
    fun clearErrors() {
        setState { copy(waiterSignInError = "", clockInError = "", clockOutError = "") }
    }

    suspend fun waiterSignIn(userPin: String) {
        setState { copy(waiterSignInResponse = Loading(), waiterSignInError = "") }
        val storeId = prefs.store?.id ?: 0
        storeUserLoginUseCase(userPin = userPin, storeId = storeId).execute { response ->
            when (response) {
                is Success -> {
                    when(response()){
                        is Fail<*> ->{
                            copy(
                                waiterSignInResponse = Fail(Throwable((response() as Fail<*>).error.message)),
                                waiterSignInError = (response() as Fail<*>).error.message ?: "Network error occurred")
                        }
                        is Success<*> -> {
                            val loginResponse = response()() ?: LoginResponse()
                            prefs.selectedWaiter = loginResponse.user
                            copy(waiterSignInResponse = response())
                        }
                        else ->{
                            copy(waiterSignInResponse = response())
                        }
                    }
                }
                is Fail -> {
                    copy(
                        waiterSignInError = response.error.message ?: "Network error occurred"
                    )
                }
                else -> {
                    copy(
                       waiterSignInResponse = Uninitialized
                    )
                }
            }
        }
    }

    suspend fun clockIn(userPin: String) {
        setState { copy(clockInResponse = Loading(), clockInError = "") }
        val storeId = prefs.store?.id ?: 0
        clockInUserTimeUseCase(userPin = userPin, storeId = storeId)
        .execute { response ->
            when (response) {
                is Success -> {
                    when(response()){
                        is Fail<*> ->{
                            copy(
                                clockInResponse = Fail(Throwable((response() as Fail<*>).error.message)),
                                clockInError = (response() as Fail<*>).error.message ?: "Network error occurred")
                        }
                        is Success<*> -> {
                            val loginResponse = response()() ?: LoginResponse()
                            prefs.selectedWaiter = loginResponse.user
                            copy(clockInResponse = response())
                        }
                        else ->{
                            copy(clockInResponse = response())
                        }
                    }
                }
                is Fail -> {
                    copy(
                        clockInError = response.error.message ?: "Network error occurred"
                    )
                }
                else -> {
                    copy(
                        clockInResponse = Uninitialized
                    )
                }
            }
        }
    }

    suspend fun clockOut(userPin: String) {
        setState { copy(clockOutResponse = Loading(), clockOutError = "") }
        val storeId = prefs.store?.id ?: 0
        clockOutUserTimeUseCase(userPin = userPin, storeId = storeId)
        .execute { response ->
            when (response) {
                is Success -> {
                    when(response()){
                        is Fail<*> ->{
                            copy(
                                clockOutResponse = Fail(Throwable((response() as Fail<*>).error.message)),
                                clockOutError = (response() as Fail<*>).error.message ?: "Network error occurred")
                        }
                        is Success<*> -> {
                            val loginResponse = response()() ?: LoginResponse()
                            prefs.selectedWaiter = loginResponse.user
                            copy(clockOutResponse = response())
                        }
                        else ->{
                            copy(clockOutResponse = response())
                        }
                    }
                }
                is Fail -> {
                    copy(
                        clockOutError = response.error.message ?: "Network error occurred"
                    )
                }
                else -> {
                    copy(
                        clockOutResponse = Uninitialized
                    )
                }
            }
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<SelectUserProfileViewModel, SelectUserProfileState> {
        override fun create(state: SelectUserProfileState): SelectUserProfileViewModel
    }

    companion object :
        MavericksViewModelFactory<SelectUserProfileViewModel, SelectUserProfileState> by hiltMavericksViewModelFactory()
}

data class SelectUserProfileState(
    val waitersResponse: Async<List<User>> = Uninitialized,
    val selectedWaiter: User? = null,
    val password: String = "",
    val loginResponse: Async<LoginResponse> = Uninitialized,
    val waiterSignInResponse: Async<LoginResponse> = Uninitialized,
    val clockInResponse: Async<LoginResponse> = Uninitialized,
    val clockOutResponse: Async<LoginResponse> = Uninitialized,
    val loginError: String = "",
    val passwordError: String = "",
    val waiterSignInError: String = "",
    val clockInError: String = "",
    val clockOutError: String = ""
) : MavericksState 