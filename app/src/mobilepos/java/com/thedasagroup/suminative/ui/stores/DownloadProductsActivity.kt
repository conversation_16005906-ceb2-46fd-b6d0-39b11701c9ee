package com.thedasagroup.suminative.ui.stores

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.AttributeSet
import android.view.View
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.categories.CategoriesActivity
import com.thedasagroup.suminative.ui.products.DownloadProductsScreen
import com.thedasagroup.suminative.ui.products.DownloadProductsViewModel
import com.thedasagroup.suminative.ui.service.Actions
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.ServiceState
import com.thedasagroup.suminative.ui.service.getServiceState
import com.thedasagroup.suminative.ui.service.log
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class DownloadProductsActivity : AppCompatActivity(), MavericksView {

    val viewModel: DownloadProductsViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SumiNativeTheme {
                DownloadProductsScreen(
                    onDownloadComplete = {
                        navigateToMainScreen()
                    },
                    viewModel = viewModel
                )
            }
        }
    }

    private fun navigateToMainScreen() {
        log("Navigating to Main Screen")
        val intent = Intent(this, CategoriesActivity::class.java)
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        scheduleJob()
    }

    private fun scheduleJob(){
        actionOnService(Actions.START)
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(this) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(this, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            startService(it)
        }
    }

    override fun invalidate() {

    }
}