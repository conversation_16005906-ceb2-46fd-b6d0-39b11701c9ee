package com.thedasagroup.suminative.ui.user_profile

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.offset
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import coil.compose.AsyncImage
import coil.request.ImageRequest
import coil.request.CachePolicy
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.ui.theme.fontPoppins
import kotlinx.coroutines.launch

@Composable
fun SelectUserProfileScreen(
    viewModel: SelectUserProfileViewModel,
    onLoginSuccess: (LoginResponse) -> Unit,
    onLoginError: (String) -> Unit,
    onBackClick: () -> Unit
) {
    var enteredCode by remember { mutableStateOf("") }

    // Collect loading states
    val waiterSignInResponse by viewModel.collectAsState(SelectUserProfileState::waiterSignInResponse)
    val clockInResponse by viewModel.collectAsState(SelectUserProfileState::clockInResponse)
    val clockOutResponse by viewModel.collectAsState(SelectUserProfileState::clockOutResponse)

    // Collect error states
    val waiterSignInError by viewModel.collectAsState(SelectUserProfileState::waiterSignInError)
    val clockInError by viewModel.collectAsState(SelectUserProfileState::clockInError)
    val clockOutError by viewModel.collectAsState(SelectUserProfileState::clockOutError)

    val coroutineScope = rememberCoroutineScope()

    // Handle successful responses
    LaunchedEffect(waiterSignInResponse) {
        if (waiterSignInResponse is Success) {
            onLoginSuccess(waiterSignInResponse() ?: LoginResponse())
        }
    }

    LaunchedEffect(clockInResponse) {
        if (clockInResponse is Success) {
            onLoginSuccess(clockInResponse() ?: LoginResponse())
        }
    }

    LaunchedEffect(clockOutResponse) {
        if (clockOutResponse is Success) {
            onLoginSuccess(clockOutResponse() ?: LoginResponse())
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(ButtonColorBGGreen)), // Green background
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth(0.6f)
                .clip(RoundedCornerShape(16.dp))
                .background(Color.White)
                .padding(48.dp)
        ) {
            Column(
                modifier = Modifier.fillMaxWidth().padding(start = 50.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Header row with back button, title and logo
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back button and title
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = Color(ButtonColorDarkGreen),
                            modifier = Modifier
                                .offset(y= 42.dp)
                                .size(32.dp)
                                .clickable { onBackClick() }
                        )
                    }
                    Spacer(modifier = Modifier.weight(1f))

                }

                Spacer(modifier = Modifier.height(16.dp))

                // Error message
                val currentError = when {
                    waiterSignInError.isNotEmpty() -> waiterSignInError
                    clockInError.isNotEmpty() -> clockInError
                    clockOutError.isNotEmpty() -> clockOutError
                    else -> ""
                }

                // Reset PIN when there's an error
                LaunchedEffect(currentError) {
                    if (currentError.isNotEmpty()) {
                        enteredCode = ""
                    }
                }

                // Main content with two columns
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Left column - PIN indicators and keypad
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.weight(1f)
                    ) {

                        Text(
                            text = "Enter employee PIN",
                            color = Color.Black,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.padding(start = 12.dp)
                        )

                        Spacer(modifier = Modifier.height(8.dp))

                        if (currentError.isNotEmpty()) {
                            Text(
                                text = currentError,
                                color = Color.Red,
                                fontSize = 16.sp,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        } else {
                            Text(
                                text = "",
                                color = Color.Red,
                                fontSize = 16.sp,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.fillMaxWidth()
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // PIN indicators
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(24.dp),
                            modifier = Modifier.padding(vertical = 24.dp)
                        ) {
                            repeat(4) { index ->
                                Box(
                                    modifier = Modifier
                                        .size(24.dp)
                                        .clip(CircleShape)
                                        .background(
                                            if (index < enteredCode.length) Color(
                                                ButtonColorDarkGreen
                                            )
                                            else Color.Transparent
                                        )
                                        .border(
                                            2.dp,
                                            Color(ButtonColorDarkGreen),
                                            CircleShape
                                        )
                                )
                            }
                        }

                        // Keypad Grid
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Row 1: 1, 2, 3
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                KeypadButton("1") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "1"
                                        viewModel.clearErrors()
                                    }
                                }
                                KeypadButton("2") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "2"
                                        viewModel.clearErrors()
                                    }
                                }
                                KeypadButton("3") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "3"
                                        viewModel.clearErrors()
                                    }
                                }
                            }

                            // Row 2: 4, 5, 6
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                KeypadButton("4") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "4"
                                        viewModel.clearErrors()
                                    }
                                }
                                KeypadButton("5") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "5"
                                        viewModel.clearErrors()
                                    }
                                }
                                KeypadButton("6") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "6"
                                        viewModel.clearErrors()
                                    }
                                }
                            }

                            // Row 3: 7, 8, 9
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                KeypadButton("7") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "7"
                                        viewModel.clearErrors()
                                    }
                                }
                                KeypadButton("8") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "8"
                                        viewModel.clearErrors()
                                    }
                                }
                                KeypadButton("9") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "9"
                                        viewModel.clearErrors()
                                    }
                                }
                            }

                            // Row 4: Empty, 0, Forward
                            Row(
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                Box(
                                    modifier = Modifier.size(70.dp)
                                ) {
                                    // Empty placeholder
                                }
                                KeypadButton("0") {
                                    if (enteredCode.length < 4) {
                                        enteredCode += "0"
                                        viewModel.clearErrors()
                                    }
                                }
                                Box(
                                    modifier = Modifier
                                        .size(70.dp)
                                        .clip(RoundedCornerShape(8.dp)),
                                    contentAlignment = Alignment.Center
                                ) {

                                }
                            }
                        }
                    }

                    // Right column - Action buttons
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .weight(1f)
                            .offset((-70).dp)
                    ) {
                        Column(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            val headerImage =
                                viewModel.prefs.loginResponse?.stores?.firstOrNull { it.id == viewModel.prefs.store?.id }?.banner
                            val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${headerImage}"
                            val request = ImageRequest.Builder(LocalContext.current)
                                .data(imageUrl)
                                .crossfade(true)
                                .diskCacheKey(imageUrl)
                                .diskCachePolicy(CachePolicy.ENABLED)
                                .setHeader("Cache-Control", "max-age=31536000")
                                .build()

                            AsyncImage(
                                model = request,
                                contentDescription = "Store Logo",
                                modifier = Modifier.height(80.dp)
                            )

                            Text(
                                text = viewModel.prefs.store?.name ?: "",
                                color = Color.Black,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(top = 4.dp)
                            )
                        }
                        // Action Buttons
                        Column(
                            verticalArrangement = Arrangement.spacedBy(16.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.padding(top = 32.dp)
                        ) {
                            // Order Now Button (Filled)
                            if(false) {
                                Button(
                                    onClick = {
                                        if (enteredCode.isNotEmpty()) {
                                            coroutineScope.launch {
                                                viewModel.waiterSignIn(
                                                    enteredCode
                                                )
                                            }
                                        }
                                    },
                                    modifier = Modifier
                                        .width(220.dp)
                                        .height(60.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(ButtonColorDarkGreen),
                                        contentColor = Color.White
                                    ),
                                    shape = RoundedCornerShape(8.dp)
                                ) {
                                    if (waiterSignInResponse is Loading) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(20.dp),
                                            color = Color.White,
                                            strokeWidth = 2.dp
                                        )
                                    } else {
                                        Text(
                                            text = "ORDER NOW",
                                            fontWeight = FontWeight.Bold,
                                            fontSize = 18.sp,
                                            fontFamily = fontPoppins
                                        )
                                    }
                                }
                            }

                            // Clock In Button (Outlined)
                            OutlinedButton(
                                onClick = {
                                    if (enteredCode.isNotEmpty()) {
                                        coroutineScope.launch {
                                            viewModel.clockIn(enteredCode)
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .width(220.dp)
                                    .height(60.dp),
                                shape = RoundedCornerShape(8.dp),
                                border = BorderStroke(2.dp, Color(ButtonColorDarkGreen)),
                                colors = ButtonDefaults.outlinedButtonColors(
                                    contentColor = Color(ButtonColorDarkGreen)
                                )
                            ) {
                                if (clockInResponse is Loading) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(20.dp),
                                        color = Color(ButtonColorDarkGreen),
                                        strokeWidth = 2.dp
                                    )
                                } else {
                                    Text(
                                        text = "CLOCK IN",
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.sp,
                                        fontFamily = fontPoppins
                                    )
                                }
                            }

                            // Clock Out Button (Filled)
                            Button(
                                onClick = {
                                    if (enteredCode.isNotEmpty()) {
                                        coroutineScope.launch {
                                            viewModel.clockOut(enteredCode)
                                        }
                                    }
                                },
                                modifier = Modifier
                                    .width(220.dp)
                                    .height(60.dp),
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(ButtonColorDarkGreen),
                                    contentColor = Color.White
                                ),
                                shape = RoundedCornerShape(8.dp)
                            ) {
                                if (clockOutResponse is Loading) {
                                    CircularProgressIndicator(
                                        modifier = Modifier.size(20.dp),
                                        color = Color.White,
                                        strokeWidth = 2.dp
                                    )
                                } else {
                                    Text(
                                        text = "CLOCK OUT",
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.sp,
                                        fontFamily = fontPoppins
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun KeypadButton(
    text: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(70.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(ButtonColorDarkGreen))
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = Color.White,
            fontSize = 24.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

val ButtonColorDarkGreen = 0xFF023723
val ButtonColorBGGreen = 0xFF2E7D32