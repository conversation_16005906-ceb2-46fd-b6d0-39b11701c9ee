package com.thedasagroup.suminative.ui.common

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.thedasagroup.suminative.ui.theme.fontPoppins

@Composable
fun CartBottomBar(
    itemCount: Int,
    onCartClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp)
            .height(60.dp)
            .background(
                Color.White,
                RoundedCornerShape(8.dp)
            )
            .border(
                1.dp,
                Color(0xFFE0E0E0),
                RoundedCornerShape(8.dp)
            )
            .clickable { onCartClick() }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "$itemCount Items",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black,
                fontFamily = fontPoppins
            )
            
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = "View Cart",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins
                )
                
                Icon(
                    imageVector = Icons.Default.ArrowForward,
                    contentDescription = "View Cart",
                    tint = Color(0xFF2E7D32),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

/**
 * Helper composable to easily add cart functionality to any screen
 * 
 * @param cartItemCount Number of items in cart
 * @param onCartClick Callback when cart is clicked
 * @param content The main screen content
 */
@Composable
fun ScreenWithCartBottomBar(
    cartItemCount: Int,
    onCartClick: () -> Unit,
    content: @Composable () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize()) {
        // Main content
        content()
        
        // Bottom Cart Bar (only show if cart has items)
        if (cartItemCount > 0) {
            CartBottomBar(
                itemCount = cartItemCount,
                onCartClick = onCartClick,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
} 