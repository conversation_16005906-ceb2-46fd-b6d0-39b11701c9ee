package com.thedasagroup.suminative.ui.payment

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import android.widget.Toast
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.list.listItems
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.launcher.MavericksLauncherActivity
import com.airbnb.mvrx.viewModel
import com.pluto.Pluto
import com.pluto.plugins.network.PlutoNetworkPlugin
import com.thedasagroup.suminative.BuildConfig
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersActivity
import com.thedasagroup.suminative.ui.login.LoginActivity
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.sales.SalesActivity
import com.thedasagroup.suminative.ui.stock.StockActivity
import com.thedasagroup.suminative.ui.stores.SelectStoreActivity
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity
import com.thedasagroup.suminative.work.LogUploadManager
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class CashPaymentActivity : AppCompatActivity(), MavericksView {
    
    val viewModel: PaymentViewModel by viewModel()
    val orderViewModel: OrderScreenViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Set full screen for tablet experience
        setupFullScreen()
        
        val order = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra(ARG_ORDER, Order::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra(ARG_ORDER)
        } ?: Order()
        
        setContent {
            SumiNativeTheme {
                Scaffold(topBar = {
                    CashTopAppBar(viewModel = orderViewModel)
                },
                    content = { _ ->
                        CashPaymentScreen(
                            order = order,
                            onDismiss = {
                                setResult(RESULT_CANCELED)
                                finish()
                            },
                            onPaymentComplete = { completedOrder ->
                                val resultIntent = Intent().apply {
                                    putExtra(RESULT_ORDER, completedOrder)
                                }
                                setResult(RESULT_OK, resultIntent)
                                finish()
                            },
                            viewModel = viewModel
                        )
                    })
            }
        }
    }

    override fun invalidate() {
        // Called when the state is updated
    }
    
    private fun setupFullScreen() {
        // Enable full screen mode
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Configure window for full screen
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        // Hide system bars
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        windowInsetsController.apply {
            hide(WindowInsetsCompat.Type.systemBars())
            systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
        
        // Keep screen on during payment
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
    
    companion object {
        private const val ARG_ORDER = "extra_order"
        const val RESULT_ORDER = "result_order"
        
        fun createIntent(context: Context, order: Order): Intent {
            return Intent(context, CashPaymentActivity::class.java).apply {
                putExtra(ARG_ORDER, order)
            }
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    fun CashTopAppBar(viewModel: OrderScreenViewModel) {
        TopAppBar(
            modifier = Modifier
                .fillMaxWidth()
                .height(120.dp)
                .background(Color.White)
                .shadow(4.dp)
                .drawBehind {
                    val strokeWidth = 1.dp.toPx()
                    drawLine(
                        color = Color.LightGray,
                        start = Offset(0f, size.height - strokeWidth / 2),
                        end = Offset(size.width, size.height - strokeWidth / 2),
                        strokeWidth = strokeWidth
                    )
                },
            title = {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back Icon (Left)
                    Box(
                        modifier = Modifier
                            .background(
                                Color(0xFF2E7D32),
                                shape = CircleShape
                            )
                            .size(40.dp)
                            .clickable {
                                finish()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            imageVector = Icons.Filled.ArrowBack,
                            colorFilter = ColorFilter.tint(color = Color.White),
                            contentDescription = "Back",
                            modifier = Modifier.size(24.dp)
                        )
                    }

                    // Center Content - Store Branding
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        val store = viewModel.prefs.store
                        val imageUrl = "${BASE_DOMAIN}/dasa/streamer?name=${store?.banner}"
                        val request: ImageRequest =
                            ImageRequest.Builder(LocalContext.current.applicationContext)
                                .data(imageUrl)
                                .crossfade(true).diskCacheKey(imageUrl)
                                .diskCachePolicy(CachePolicy.ENABLED)
                                .setHeader("Cache-Control", "max-age=31536000").build()
                        AsyncImage(
                            modifier = Modifier.size(50.dp),
                            model = request,
                            contentDescription = ""
                        )
                        Text(
                            text = store?.name ?: "Store Name",
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal,
                                color = Color.Black
                            )
                        )
                    }

                    Box(
                        modifier = Modifier
                            .size(40.dp),
                        contentAlignment = Alignment.Center
                    ) {

                    }
                }

            }
        )
    }
}