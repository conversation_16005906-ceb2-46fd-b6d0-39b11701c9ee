package com.thedasagroup.suminative.ui.payment

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Backspace
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.user_profile.ButtonColorDarkGreen
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlin.collections.sumByDouble
import kotlin.let
import kotlin.math.abs
import kotlin.text.contains
import kotlin.text.dropLast
import kotlin.text.isEmpty
import kotlin.text.isNotEmpty
import kotlin.text.toDouble

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CashPaymentScreen(
    order: Order,
    onDismiss: () -> Unit,
    onPaymentComplete: (Order) -> Unit,
    viewModel: PaymentViewModel
) {
    CashPaymentDialogContent(
        order = order,
        onDismiss = onDismiss,
        onPaymentComplete = onPaymentComplete,
        viewModel = viewModel
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CashPaymentDialogContent(
    order: Order,
    onDismiss: () -> Unit,
    onPaymentComplete: (Order) -> Unit,
    viewModel: PaymentViewModel
){
    val orderState by viewModel.collectAsState(PaymentState::order)

    // Get the order object, fall back to the passed order if orderState isn't available
    val currentOrder = orderState()?.let { it } ?: order

    // Make sure we use the correct totalPrice by ensuring it's properly calculated
    val totalAmount = currentOrder.totalPrice ?: currentOrder.carts?.sumByDouble {
        (it.netPayable ?: 0.0) + (it.tax ?: 0.0)
    } ?: 0.0

    val amountGivenText by viewModel.collectAsState(PaymentState::amountGivenText)
    val showChangeCalculation by viewModel.collectAsState(PaymentState::showChangeCalculation)

    // Calculate change amount
    val changeAmount = try {
        if (amountGivenText.isNotEmpty()) {
            amountGivenText.toDouble() - totalAmount
        } else {
            0.0
        }
    } catch (e: NumberFormatException) {
        0.0
    }

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = Color.White
    ) {
        Row(modifier = Modifier.fillMaxSize().padding(top = 100.dp)) {
            // Left side - Payment info
            Box(
                modifier = Modifier
                    .weight(1.2f)
                    .fillMaxSize()
                    .background(Color(0xFFB8D8B8)) // Light green background from image
                    .padding(32.dp),
                contentAlignment = Alignment.Center
            ) {
                // Payment info table
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .border(
                            width = 1.dp,
                            color = Color.Black,
                            shape = RoundedCornerShape(0.dp)
                        )
                ) {
                    // Row 1: CASH PAYMENT
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(width = 1.dp, color = Color.Black)
                    ) {
                        Text(
                            text = "CASH PAYMENT",
                            modifier = Modifier
                                .weight(1f)
                                .padding(16.dp),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "£${totalAmount.transformDecimal()}",
                            modifier = Modifier
                                .padding(16.dp),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.End
                        )
                    }
                    
                    // Row 2: AMOUNT COLLECTED
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(width = 1.dp, color = Color.Black)
                    ) {
                        Text(
                            text = "AMOUNT COLLECTED",
                            modifier = Modifier
                                .weight(1f)
                                .padding(16.dp),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "£${amountGivenText.ifEmpty { "0.0" }.toDouble().transformDecimal()}",
                            modifier = Modifier
                                .padding(16.dp),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.End
                        )
                    }
                    
                    // Row 3: AMOUNT TO BE RETURNED
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .border(width = 1.dp, color = Color.Black)
                    ) {
                        Text(
                            text = "AMOUNT TO BE RETURNED",
                            modifier = Modifier
                                .weight(1f)
                                .padding(16.dp),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "£${changeAmount.transformDecimal()}",
                            modifier = Modifier
                                .padding(16.dp),
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.End
                        )
                    }
                }
            }
            
            // Right side - Keypad
            Column(
                modifier = Modifier
                    .weight(0.8f)
                    .fillMaxSize()
                    .padding(16.dp)
            ) {
                // Number pad grid (3x4)
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.White)
                ) {

                    // Header with amount display
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                            .border(
                                width = 1.dp,
                                color = Color.LightGray,
                                shape = RoundedCornerShape(8.dp)
                            )
                            .background(Color.White),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "£${amountGivenText.ifEmpty { "0.0" }}",
                            fontSize = 32.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier.padding(16.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    NumberPad(
                        onNumberClick = { number ->
                            val newAmount = amountGivenText + number
                            // Check if the new amount would exceed 100,000
                            try {
                                val numericValue = newAmount.toDouble()
                                if (numericValue <= 100000.0) {
                                    viewModel.updateAmountGivenText(newAmount)
                                    if (newAmount.isNotEmpty()) {
                                        viewModel.showChangeCalculation(true)
                                    }
                                }
                                // If it exceeds 100,000, ignore the input
                            } catch (e: NumberFormatException) {
                                // If it's not a valid number, allow it for now (might be in the middle of typing)
                                if (newAmount.length <= 10) { // Reasonable length limit
                                    viewModel.updateAmountGivenText(newAmount)
                                    if (newAmount.isNotEmpty()) {
                                        viewModel.showChangeCalculation(true)
                                    }
                                }
                            }
                        },
                        onDecimalClick = {
                            if (!amountGivenText.contains(".")) {
                                val newAmount = if (amountGivenText.isEmpty()) "0." else "$amountGivenText."
                                // Check if the amount with decimal would exceed 100,000
                                try {
                                    val numericValue = newAmount.toDouble()
                                    if (numericValue <= 100000.0) {
                                        viewModel.updateAmountGivenText(newAmount)
                                        viewModel.showChangeCalculation(true)
                                    }
                                } catch (e: NumberFormatException) {
                                    // Allow decimal if it's still reasonable length
                                    if (newAmount.length <= 10) {
                                        viewModel.updateAmountGivenText(newAmount)
                                        viewModel.showChangeCalculation(true)
                                    }
                                }
                            }
                        },
                        onBackspaceClick = {
                            val newAmount = if (amountGivenText.isNotEmpty()) {
                                amountGivenText.dropLast(1)
                            } else {
                                ""
                            }
                            viewModel.updateAmountGivenText(newAmount)
                            viewModel.showChangeCalculation(newAmount.isNotEmpty())
                        },
                        onConfirmClick = {
                            val updatedOrder = currentOrder.copy(paymentType = 5)
                            onPaymentComplete(updatedOrder)
                            onDismiss()
                        },
                        isConfirmEnabled = changeAmount >= 0 && amountGivenText.isNotEmpty()
                    )
                }
            }
            
            // Third column - Arrow button
            Box(
                modifier = Modifier
                    .width(100.dp)
                    .fillMaxHeight()
                    .padding(vertical = 16.dp, horizontal = 4.dp),
                contentAlignment = Alignment.Center
            ) {
                Box(
                    modifier = Modifier
                        .width(100.dp)
                        .padding(top = 10.dp)
                        .fillMaxHeight()
                        .clip(RoundedCornerShape(8.dp))
                        .border(
                            width = 5.dp,
                            color = Color(ButtonColorDarkGreen),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .background(Color.White)
                        .clickable(
                            enabled = changeAmount >= 0 && amountGivenText.isNotEmpty()
                        ) {
                            val updatedOrder = currentOrder.copy(paymentType = 5)
                            onPaymentComplete(updatedOrder)
                            onDismiss()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowRight,
                        contentDescription = "Confirm Payment",
                        tint = Color(ButtonColorDarkGreen),
                        modifier = Modifier.size(100.dp)
                    )
                }
            }
        }
    }
}