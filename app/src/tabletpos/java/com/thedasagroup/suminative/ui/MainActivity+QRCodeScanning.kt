package com.thedasagroup.suminative.ui

import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager

fun MainActivity.startQRCodeScan(){

    //Note: for the older version SunmiScanner v1.x.x
    val intent = Intent("com.summi.scan")


    //Note: if the device has ScannerHead v4.4.4 and above versions
    if (hasScanner(this)) {
        intent.setAction("com.sunmi.scanner.qrscanner")
    }

    intent.putExtra("PLAY_SOUND", true) // Sound prompt after scan completes, true by default
    intent.putExtra("PLAY_VIBRATE", false)

    //Vibrate when scan completes, false by default. At present, M1 hardware supports this configuration for vibration, while V1 does not support.
    intent.putExtra(
        "IDENTIFY_MORE_CODE",
        false
    ) // Recognize multiple QR codes in the image, false by default
    intent.putExtra(
        "IS_SHOW_SETTING",
        true
    ) // Whether to display the Settings button in the upper right corner, true by default
    intent.putExtra(
        "IS_SHOW_ALBUM",
        true
    ) // Whether to display the button "select picture from album", true by default
    intent.putExtra(
        "IDENTIFY_INVERSE",
        true
    ) // Allow to read the inverse color QR code, true by default
    intent.putExtra(
        "IS_EAN_8_ENABLE",
        true
    ) //Allow to read the EAN-8 barcode, true (allow) by default
    intent.putExtra(
        "IS_UPC_E_ENABLE",
        true
    ) //Allow to read the UPC-E barcode, true (allow) by default
    intent.putExtra(
        "IS_ISBN_10_ENABLE",
        false
    ) //Allow to read the ISBN-10 (from EAN-13) barcode, false (not allowed) by default
    intent.putExtra(
        "IS_CODE_11_ENABLE",
        true
    ) //Allow to read the CODE-11 barcode, false (not allowed) by default
    intent.putExtra(
        "IS_UPC_A_ENABLE",
        true
    ) //Allow to read the UPC-A barcode, true (allow) by default
    intent.putExtra(
        "IS_EAN_13_ENABLE",
        true
    ) //Allow to read the AN-13 barcode, true (allow) by default
    intent.putExtra(
        "IS_ISBN_13_ENABLE",
        true
    ) //Allow to read the ISBN-13 (from EAN-13) barcode, true (allow) by default
    intent.putExtra(
        "IS_INTERLEAVED_2_OF_5_ENABLE",
        true
    ) //Allow to read the Interleaved 2 of 5 barcode, false (not allowed) by default
    intent.putExtra(
        "IS_CODE_128_ENABLE",
        true
    ) //Allow to read the ECode 128 barcode, true (allow) by default
    intent.putExtra(
        "IS_CODABAR_ENABLE",
        true
    ) //Allow to read the Codabar barcode, true (allow) by default
    intent.putExtra(
        "IS_CODE_39_ENABLE",
        true
    ) //Allow to read the Code 39 barcode, true (allow) by default
    intent.putExtra(
        "IS_CODE_93_ENABLE",
        true
    ) //Allow to read the Code 93 barcode, true (allow) by default
    intent.putExtra(
        "IS_DATABAR_ENABLE",
        true
    ) //Allow to read the DataBar (RSS-14) barcode, true (allow) by default
    intent.putExtra(
        "IS_DATABAR_EXP_ENABLE",
        true
    ) //Allow to read the DataBar Expanded barcode, true (allow) by default
    intent.putExtra(
        "IS_Micro_PDF417_ENABLE",
        true
    ) //Allow to read the Micro PDF417 barcode, true (allow) by default
    intent.putExtra(
        "IS_MicroQR_ENABLE",
        true
    ) //Allow to read the Micro QR Code barcode, true (allow) by default
    intent.putExtra("IS_OPEN_LIGHT", true) // Whether to display the flashlight, false by default
    intent.putExtra("SCAN_MODE", false) // Whether cycle mode, false by default
    intent.putExtra("IS_QR_CODE_ENABLE", true) // Allow to read the QR code, true (allow) by default
    intent.putExtra(
        "IS_PDF417_ENABLE",
        true
    ) // Allow to read the PDF417 barcode, false (not allowed) by default
    intent.putExtra(
        "IS_DATA_MATRIX_ENABLE",
        true
    ) // Allow to read the DataMatrix code, false (not allowed) by default
    intent.putExtra(
        "IS_AZTEC_ENABLE",
        true
    ) // Allow to read the AZTEC code, false (not allowed) by default
    intent.putExtra(
        "IS_Hanxin_ENABLE",
        false
    ) // Allow to read the Hanxin code, false (not allowed) by default
    sumniQRCodeScanLauncher.launch(intent)
}

fun getPackageInfo(context: Context, pkg: String): PackageInfo? {
    var packageInfo: PackageInfo?
    try {
        packageInfo = context.getPackageManager().getPackageInfo(pkg, 0)
    } catch (e: PackageManager.NameNotFoundException) {
        packageInfo = null
        e.printStackTrace()
    }
    return packageInfo
}

fun hasScanner(ctx: Context): Boolean {
    val info = getPackageInfo(ctx, "com.sunmi.scanner")
    return info != null && compareVer(info.versionName, "4.4.4", true, 3)
}

fun compareVer(nVer: String, oVer: String, isEq: Boolean, bit: Int): Boolean {
    if (nVer.isEmpty() || oVer.isEmpty()) return false
    val nArr: Array<String?> =
        nVer.split("[.]".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
    val oArr: Array<String?> =
        oVer.split("[.]".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
    if (nArr.size < bit || oArr.size < bit) return false
    var vup = false
    for (i in 0..<bit) {
        val n = nArr[i]!!.toInt()
        val o = oArr[i]!!.toInt()
        if (n >= o) {
            if (n > o) {
                vup = true
                break
            } else if (isEq && i == (bit - 1)) {
                vup = true
                break
            }
        } else {
            break
        }
    }
    return vup
}

