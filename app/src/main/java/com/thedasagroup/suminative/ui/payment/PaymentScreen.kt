package com.thedasagroup.suminative.ui.payment

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.my_guava.failure.GuavaFailResponse
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

val TAG: String = "PaymentScreen"

@Composable
fun PaymentScreen(
    paymentViewModel: PaymentViewModel,
    onMakePaymentClickSuccess: (Order2) -> Unit = {},
    onMakePaymentClickFail: (GuavaFailResponse) -> Unit = {},
    onPaymentCancelled: () -> Unit = {},
    onSumUpPaymentClick: ((Order) -> Unit)? = null
) {
    val order by paymentViewModel.collectAsState(PaymentState::order)
    val makePaymentResponse by paymentViewModel.collectAsState(PaymentState::makePaymentResponse)
    val checkStatusSession by paymentViewModel.collectAsState(PaymentState::checkStatusSession)
    val terminalListResponse by paymentViewModel.collectAsState(PaymentState::terminalListResponse)
    val checkStatusLoopResponse by paymentViewModel.collectAsState(PaymentState::checkStatusLoopResponse)
    val cancelingPayment by paymentViewModel.collectAsState(PaymentState::cancelingPayment)
    val paymentOrderResponse by paymentViewModel.collectAsState(PaymentState::paymentOrderResponse)
    val selectedTerminal by paymentViewModel.collectAsState(PaymentState::selectedTerminal)
    val coroutineScope = rememberCoroutineScope()
    
    // Handle checkStatusLoopResponse changes
    LaunchedEffect(checkStatusLoopResponse) {
        when (checkStatusLoopResponse) {
            is Success -> {
                onMakePaymentClickSuccess(paymentOrderResponse()?.order ?: Order2())
            }
            is Fail -> {
                onMakePaymentClickFail(
                    GuavaFailResponse(
                        error = (checkStatusLoopResponse as Fail<Unit>).error.message ?: "Payment verification failed"
                    )
                )
            }
            else -> { /* Do nothing for Loading or Uninitialized */ }
        }
    }
    
    // Handle cancelingPayment changes
    LaunchedEffect(cancelingPayment) {
        when (cancelingPayment) {
            is Success -> {
                onPaymentCancelled()
            }
            is Fail -> {
                onMakePaymentClickFail(
                    GuavaFailResponse(
                        error = (cancelingPayment as Fail<Unit>).error.message ?: "Failed to cancel payment"
                    )
                )
            }
            else -> { /* Do nothing for Loading or Uninitialized */ }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF0A2540))
    ) {
        // Add close button in the top-right corner
        IconButton(
            onClick = {
                // Cancel any active payment session and close the fragment
                paymentViewModel.cancelPayment(makePaymentResponse()?.id)
            },
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp)
                .size(48.dp)
                .background(Color.White.copy(alpha = 0.15f), RoundedCornerShape(50))
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close payment screen",
                tint = Color.White,
                modifier = Modifier.size(24.dp)
            )
        }
        
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterVertically)
        ) {
            // Amount card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF1E3A5F)
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "£ ${order()?.totalPrice?.transformDecimal()}",
                        fontSize = 32.sp,
                        fontFamily = fontPoppins,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )

                    Text(
                        text = "amount to pay",
                        fontSize = 16.sp,
                        fontFamily = fontPoppins,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    // Display selected terminal information
                    selectedTerminal?.let { terminal ->
                        Text(
                            text = "Terminal: ${terminal.description ?: "Unknown"}",
                            fontSize = 14.sp,
                            fontFamily = fontPoppins,
                            color = Color.White.copy(alpha = 0.7f),
                            textAlign = TextAlign.Center
                        )
                    } ?: run {
                        Text(
                            text = "Please select a payment terminal",
                            fontSize = 14.sp,
                            fontFamily = fontPoppins,
                            color = Color.Yellow,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Terminal selection card
            when (terminalListResponse) {
                is Success -> {
                    val terminals = terminalListResponse()?.data?.list
                    if (!terminals.isNullOrEmpty()) {
                        TerminalSelectionCard(
                            terminals = terminals,
                            selectedTerminal = selectedTerminal,
                            onTerminalSelected = { terminal ->
                                paymentViewModel.selectTerminal(terminal)
                                coroutineScope.launch {
                                    paymentViewModel.makePayment(
                                        order = order() ?: Order(),
                                        terminal = terminal
                                    )
                                }
                            }
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
                is Loading -> {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        shape = RoundedCornerShape(12.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFF1E3A5F)
                        )
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                color = Color.White,
                                strokeWidth = 2.dp
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Loading terminals...",
                                fontSize = 16.sp,
                                fontFamily = fontPoppins,
                                color = Color.White
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                }
                else -> {
                    // Show error or empty state for terminals
                }
            }

            when {
                cancelingPayment is Loading -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(color = Color.White)
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Canceling payment...",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins,
                            color = Color.White,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                terminalListResponse is Fail -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Unable to load payment terminals",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins,
                            color = Color.Red,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = {
                                coroutineScope.launch {
                                    paymentViewModel.getTerminalList()
                                }
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.White
                            ),
                            shape = RoundedCornerShape(8.dp),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp)
                                .height(48.dp)
                        ) {
                            Text(
                                text = "Retry",
                                fontSize = 16.sp,
                                fontFamily = fontPoppins,
                                fontWeight = FontWeight.SemiBold,
                                color = Color(0xFF0A2540)
                            )
                        }
                    }
                }
                terminalListResponse is Success && terminalListResponse()?.data?.list.isNullOrEmpty() -> {
                    Text(
                        text = "No payment terminals available.\nPlease check your terminal connection and try again.",
                        fontSize = 16.sp,
                        fontFamily = fontPoppins,
                        color = Color.Red,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Button(
                        onClick = {
                            coroutineScope.launch {
                                paymentViewModel.getTerminalList()
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White
                        ),
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp)
                            .height(48.dp)
                    ) {
                        Text(
                            text = "Retry",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins,
                            fontWeight = FontWeight.SemiBold,
                            color = Color(0xFF0A2540)
                        )
                    }
                }
                checkStatusLoopResponse is Loading -> {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Text(
                            text = "Processed Payment on Device",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins,
                            color = Color.Green,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))

                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(color = Color.White)
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Checking payment status...",
                                fontSize = 14.sp,
                                fontFamily = fontPoppins,
                                color = Color.White.copy(alpha = 0.7f)
                            )
                        }
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Cancel button
                        Button(
                            onClick = {
                                paymentViewModel.cancelPayment(makePaymentResponse()?.id)
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.Red.copy(alpha = 0.8f)
                            ),
                            modifier = Modifier
                                .padding(horizontal = 16.dp)
                                .height(48.dp)
                        ) {
                            Text(
                                text = "Cancel Payment",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }
                checkStatusLoopResponse is Fail -> {
                    PaymentSessionTryAgain(
                        onClick = {
                            // Retry the payment by calling the makePayment API again
                            coroutineScope.launch {
                                paymentViewModel.makePayment(
                                    order = order() ?: Order(),
                                    terminal = selectedTerminal ?: Terminal()
                                ).collectLatest {
                                    // Payment response is handled by the ViewModel
                                }
                            }
                        }
                    )
                }
                makePaymentResponse is Loading -> {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(color = Color.White)
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "Processing payment...",
                            fontSize = 14.sp,
                            fontFamily = fontPoppins,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                        
                        Spacer(modifier = Modifier.height(16.dp))
                        
                        // Cancel button
                        Button(
                            onClick = {
                                paymentViewModel.cancelPayment(sessionId = makePaymentResponse()?.id)
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.Red.copy(alpha = 0.8f)
                            ),
                            modifier = Modifier
                                .padding(horizontal = 16.dp)
                                .height(48.dp)
                        ) {
                            Text(
                                text = "Cancel Payment",
                                color = Color.White,
                                fontSize = 16.sp,
                                fontFamily = fontPoppins
                            )
                        }
                    }
                }
                makePaymentResponse is Fail -> {
                    PaymentSessionTryAgain(
                        onClick = {
                            coroutineScope.launch {
                                paymentViewModel.makePayment(
                                    order = order() ?: Order(),
                                    terminal = selectedTerminal ?: Terminal()
                                ).collectLatest {
                                    // Payment response is handled by the ViewModel
                                }
                            }
                        }
                    )
                }
                makePaymentResponse !is Success && checkStatusLoopResponse !is Loading && terminalListResponse is Success && !terminalListResponse()?.data?.list.isNullOrEmpty() && selectedTerminal != null -> {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // MyGuava Payment Button
                        PaymentMethodButton(
                            text = "Pay with MyGuava",
                            onClick = {
                                coroutineScope.launch {
                                    paymentViewModel.makePayment(
                                        order = order() ?: Order(),
                                        terminal = selectedTerminal ?: Terminal()
                                    ).collectLatest {
                                        // Payment response is handled by the ViewModel
                                    }
                                }
                            }
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // SumUp Payment Button
                        PaymentMethodButton(
                            text = "Pay with SumUp",
                            onClick = {
                                // We need to handle this through the Fragment's activity result
                                // The Fragment will handle the SumUp payment flow
                                onSumUpPaymentClick?.invoke(order() ?: Order())
                            }
                        )
                    }
                }
                makePaymentResponse !is Success && checkStatusLoopResponse !is Loading && terminalListResponse is Success && !terminalListResponse()?.data?.list.isNullOrEmpty() && selectedTerminal == null -> {
                    Text(
                        text = "Please select a payment terminal to proceed",
                        fontSize = 16.sp,
                        fontFamily = fontPoppins,
                        color = Color.Yellow,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun PaymentSessionTryAgain(
    onClick: () -> Unit
) {
    val isRetrying = remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = "Payment failed. Please try again.",
            fontSize = 16.sp,
            fontFamily = fontPoppins,
            color = Color.Red,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(8.dp))
        
        if (isRetrying.value) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator(color = Color.White)
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "Retrying...",
                    fontSize = 14.sp,
                    fontFamily = fontPoppins,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        } else {
            PaymentMethodButton(
                text = "Try Again!",
                onClick = {
                    isRetrying.value = true
                    onClick()
                }
            )
        }
    }
}

@Composable
fun PaymentMethodButton(
    text: String,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .height(56.dp),
        shape = RoundedCornerShape(8.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color.White
        )
    ) {
        Text(
            text = text,
            fontSize = 18.sp,
            fontFamily = fontPoppins,
            fontWeight = FontWeight.SemiBold,
            color = Color(0xFF150B3D) // Dark purple text for contrast on white
        )
    }
}

@Composable
fun TerminalSelectionCard(
    terminals: List<Terminal>,
    selectedTerminal: Terminal?,
    onTerminalSelected: (Terminal) -> Unit
) {
    if(terminals.isNotEmpty() && terminals.size == 1){
        onTerminalSelected(terminals[0])
    }
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF1E3A5F)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "Select Payment Terminal",
                fontSize = 18.sp,
                fontFamily = fontPoppins,
                fontWeight = FontWeight.SemiBold,
                color = Color.White,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(terminals) { terminal ->
                    TerminalItem(
                        terminal = terminal,
                        isSelected = selectedTerminal?.id == terminal.id,
                        onSelected = { onTerminalSelected(terminal) }
                    )
                }
            }
        }
    }
}

@Composable
fun TerminalItem(
    terminal: Terminal,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onSelected() }
            .border(
                width = if (isSelected) 2.dp else 0.dp,
                color = if (isSelected) Color.Green else Color.Transparent,
                shape = RoundedCornerShape(8.dp)
            ),
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) Color(0xFF2A4A6B) else Color(0xFF1A3A5A)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = terminal.description ?: "Unknown Terminal",
                        fontSize = 16.sp,
                        fontFamily = fontPoppins,
                        fontWeight = FontWeight.Medium,
                        color = Color.White
                    )
                    
                    if (!terminal.identifier.isNullOrEmpty()) {
                        Text(
                            text = "ID: ${terminal.identifier}",
                            fontSize = 12.sp,
                            fontFamily = fontPoppins,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                    }
                    
                    if (!terminal.serialNumber.isNullOrEmpty()) {
                        Text(
                            text = "Serial: ${terminal.serialNumber}",
                            fontSize = 12.sp,
                            fontFamily = fontPoppins,
                            color = Color.White.copy(alpha = 0.7f)
                        )
                    }
                }
                
                // Status indicator
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            color = if (terminal.available == true) Color.Green else Color.Red,
                            shape = RoundedCornerShape(50)
                        )
                )
            }
            
            if (isSelected) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "✓ Selected",
                    fontSize = 12.sp,
                    fontFamily = fontPoppins,
                    fontWeight = FontWeight.Medium,
                    color = Color.Green
                )
            }
        }
    }
}