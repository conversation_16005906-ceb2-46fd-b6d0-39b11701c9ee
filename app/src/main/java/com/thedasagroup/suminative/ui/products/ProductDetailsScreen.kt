package com.thedasagroup.suminative.ui.products

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.AddShoppingCart
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material.icons.filled.RemoveCircle
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledIconButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.RadioButton
import androidx.compose.material3.SheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.withContext
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun ProductDetailsBottomSheet(
    isBottomSheetVisible: Boolean,
    sheetState: SheetState,
    onDismiss: () -> Unit,
    cart: Cart,
    viewModel: ProductsScreenViewModel,
    updateStock: (Int, OptionDetails, StoreItem) -> Unit,
    addToCart: (StoreItem, OptionDetails, Cart?, Boolean) -> Unit,
    selectOptionCondition2: (Option?, Boolean, OptionSet, OptionDetails, StoreItem) -> Unit,
    selectOptionCondition1: (Option?, OptionSet, OptionDetails, StoreItem) -> Unit
) {

    val state by viewModel.collectAsState()

    if (isBottomSheetVisible) {

        val stockItem1 = cart.storeItem?.let { viewModel.run { it.toStockItem() } } ?: StockItem()
        val storeItem = cart.storeItem ?: StoreItem()
        val appliedPrefill by viewModel.collectAsState(ProductsScreenState::appliedPrefill)
        val optionDetails by viewModel.collectAsState(ProductsScreenState::optionDetailsResponse)
        val listOptionSet = optionDetails()?.optionSets ?: emptyList()
        val productTotal by viewModel.collectAsState(ProductsScreenState::productTotal)
        val stock by viewModel.collectAsState(ProductsScreenState::stock)

        LaunchedEffect(key1 = cart.uuid, block = {
            withContext(Dispatchers.IO) {
                viewModel.resetStock()
                val updatedState = viewModel.updateAppliedPrefill(cart = cart, state = state)
                viewModel.getOptionDetails(storeItem.id ?: 0, stockItem = stockItem1, state = updatedState)
                    .collectLatest { response ->

                    }
            }
        })
        // Check if all required options (condition 1) are selected
        val areRequiredOptionsSelected = remember(optionDetails) {
            val condition1OptionSets = listOptionSet.filter { it.condition == 1 }
            // If there are no condition 1 option sets, then no validation is needed
            if (condition1OptionSets.isEmpty()) {
                true
            } else {
                // All condition 1 option sets must have at least one option selected
                condition1OptionSets.all { optionSet ->
                    optionSet.options.any { option -> option?.optionchecked == true }
                }
            }
        }

        // Get list of unselected required option sets for better user feedback
        val unselectedRequiredOptionSets = remember(optionDetails) {
            listOptionSet.filter { optionSet ->
                optionSet.condition == 1 && optionSet.options.none { option -> option?.optionchecked == true }
            }
        }

        // Full Screen Dialog
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(color = Color.White)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
            ) {
                // Left Side - Product Image (Full)
                Column(
                    modifier = Modifier
                        .weight(0.5f)
                        .fillMaxHeight(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    val imageUrl = "$BASE_DOMAIN/dasa/streamer?name=${storeItem.pic}"
                    val request: ImageRequest =
                        ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl)
                            .crossfade(true)
                            .diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
                            .setHeader("Cache-Control", "max-age=31536000").build()

                    AsyncImage(
                        model = request,
                        contentDescription = storeItem.name,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(16.dp)),
                        contentScale = ContentScale.Crop
                    )
                }

                Spacer(modifier = Modifier.width(24.dp))

                // Right Side - Product Details
                when (optionDetails) {
                    is Loading -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(color = Color(0xFF2E7D32))
                        }
                    }
                    else -> {
                        Column(
                            modifier = Modifier
                                .weight(0.5f)
                                .fillMaxHeight()
                        ) {
                            // Product Title and Base Price
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // Product Title aligned with options
                                Text(
                                    text = (storeItem.name ?: "").uppercase(),
                                    style = MaterialTheme.typography.headlineLarge,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                    color = Color.Black,
                                    modifier = Modifier.weight(0.7f)
                                )

                                // Product Price aligned with option prices
                                Text(
                                    text = "£${storeItem.price?.transformDecimal() ?: "0.00"}",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                    color = Color.Black,
                                    modifier = Modifier.weight(0.3f),
                                    textAlign = androidx.compose.ui.text.style.TextAlign.End
                                )
                            }

                            // Scrollable Options Area
                            LazyColumn(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .weight(1f)
                                    .background(Color.White, RoundedCornerShape(8.dp))
                                    .padding(0.dp)
                            ) {

                                items(listOptionSet.size) { index ->
                                    when (listOptionSet.get(index = index).condition) {
                                        0 -> {
                                            Text(
                                                text = listOptionSet.get(index).name ?: "",
                                                style = MaterialTheme.typography.headlineLarge,
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                color = Color(CATEGORY_GREEN_COLOR),
                                                modifier = Modifier.padding(vertical = 8.dp)
                                            )
                                            Text(
                                                text = "CHOOSE ANY NUMBER",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = Color.Gray,
                                                modifier = Modifier.padding(bottom = 16.dp)
                                            )
                                            listOptionSet.get(index).let {
                                                OptionsUICondition0(
                                                    optionSet = it,
                                                    onOptionSelected = { selectedOption, isChecked, optionSet ->
                                                        selectOptionCondition2(
                                                            selectedOption,
                                                            isChecked,
                                                            optionSet,
                                                            optionDetails() ?: OptionDetails(),
                                                            storeItem,
                                                        )
                                                    },
                                                    viewModel = viewModel,
                                                    onUpdateStock = { option, optionSet, optionStock ->
                                                        viewModel.updateOptionStock(
                                                            option = option ?: Option(),
                                                            currentOptionSet = optionSet,
                                                            stock = stock,
                                                            optionStock = optionStock,
                                                            optionDetails() ?: OptionDetails(),
                                                            stockItem = storeItem
                                                        )
                                                    })
                                            }
                                        }

                                        1 -> {
                                            Row(
                                                modifier = Modifier.padding(vertical = 8.dp),
                                                verticalAlignment = Alignment.CenterVertically
                                            ) {
                                                Text(
                                                    text = listOptionSet.get(index).name ?: "",
                                                    style = MaterialTheme.typography.headlineLarge,
                                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                    color = Color(CATEGORY_GREEN_COLOR)
                                                )
                                                Text(
                                                    text = " *",
                                                    style = MaterialTheme.typography.headlineLarge,
                                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                    color = Color.Red
                                                )
                                            }
                                            Text(
                                                text = "ONE ITEM REQUIRED",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = Color.Red,
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                modifier = Modifier.padding(bottom = 16.dp)
                                            )
                                            listOptionSet.get(index).let { currentOptionSet ->
                                                val isOptionSetSelected =
                                                    currentOptionSet.options.any { option -> option?.optionchecked == true }

                                                Column(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .background(
                                                            color = if (!isOptionSetSelected) Color(
                                                                0xFFFFEBEE
                                                            ) else Color.Transparent,
                                                            shape = RoundedCornerShape(8.dp)
                                                        )
                                                        .border(
                                                            width = if (!isOptionSetSelected) 1.dp else 0.dp,
                                                            color = if (!isOptionSetSelected) Color(
                                                                0xFFFFCDD2
                                                            ) else Color.Transparent,
                                                            shape = RoundedCornerShape(8.dp)
                                                        )
                                                        .padding(if (!isOptionSetSelected) 8.dp else 0.dp)
                                                ) {
                                                    OptionsUICondition1(
                                                        optionSet = currentOptionSet,
                                                        onOptionSelected = { selectedOption, optionSet ->
                                                            selectOptionCondition1(
                                                                selectedOption,
                                                                optionSet,
                                                                optionDetails() ?: OptionDetails(),
                                                                storeItem,
                                                            )
                                                        },
                                                        viewModel = viewModel
                                                    )
                                                }
                                            }
                                        }

                                        2 -> {
                                            Text(
                                                text = listOptionSet.get(index).name ?: "",
                                                style = MaterialTheme.typography.headlineLarge,
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                color = Color(CATEGORY_GREEN_COLOR),
                                                modifier = Modifier.padding(vertical = 8.dp)
                                            )
                                            Text(
                                                text = "CHOOSE ANY NUMBER",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = Color.Gray,
                                                modifier = Modifier.padding(bottom = 16.dp)
                                            )
                                            listOptionSet.get(index).let {
                                                OptionsUICondition2(
                                                    optionSet = it,
                                                    onOptionSelected = { selectedOption, isChecked, optionSet ->
                                                        selectOptionCondition2(
                                                            selectedOption,
                                                            isChecked,
                                                            optionSet,
                                                            optionDetails() ?: OptionDetails(),
                                                            storeItem,
                                                        )
                                                    },
                                                    viewModel = viewModel,
                                                    onUpdateStock = { option, optionSet, optionStock ->
                                                        viewModel.updateOptionStock(
                                                            option = option ?: Option(),
                                                            currentOptionSet = optionSet,
                                                            stock = stock,
                                                            optionStock = optionStock,
                                                            optionDetails = optionDetails()
                                                                ?: OptionDetails(),
                                                            stockItem = storeItem
                                                        )
                                                    })
                                            }
                                        }

                                        3 -> {
                                            Text(
                                                text = listOptionSet.get(index).name ?: "",
                                                style = MaterialTheme.typography.headlineLarge,
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                color = Color(CATEGORY_GREEN_COLOR),
                                                modifier = Modifier.padding(vertical = 8.dp)
                                            )
                                            Text(
                                                text = "ANY NUMBER REQUIRED",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = Color.Gray,
                                                modifier = Modifier.padding(bottom = 16.dp)
                                            )
                                            listOptionSet.get(index).let {
                                                OptionsUICondition2(
                                                    optionSet = it,
                                                    onOptionSelected = { selectedOption, isChecked, optionSet ->
                                                        if (selectedOption != null) {
                                                            if (isChecked) {
                                                                viewModel.addSelectedOptionCondition2(
                                                                    optionDetails = optionDetails()
                                                                        ?: OptionDetails(),
                                                                    option = selectedOption,
                                                                    currentOptionSet = optionSet,
                                                                    stockItem = storeItem,
                                                                    stock = stock,
                                                                    state = state
                                                                )
                                                            } else {
                                                                viewModel.removeSelectedOptionCondition2(
                                                                    optionDetails = optionDetails()
                                                                        ?: OptionDetails(),
                                                                    selectedOption,
                                                                    currentOptionSet = optionSet,
                                                                    stockItem = storeItem,
                                                                    stock = stock,
                                                                    state = state
                                                                )
                                                            }
                                                        }
                                                    },
                                                    viewModel = viewModel,
                                                    onUpdateStock = { option, optionSet, optionStock ->
                                                        viewModel.updateOptionStock(
                                                            option = option ?: Option(),
                                                            currentOptionSet = optionSet,
                                                            stock = stock,
                                                            optionStock = optionStock,
                                                            optionDetails = optionDetails()
                                                                ?: OptionDetails(),
                                                            stockItem = storeItem
                                                        )
                                                    })
                                            }
                                        }

                                        5 -> {
                                            Text(
                                                text = listOptionSet.get(index).name ?: "",
                                                style = MaterialTheme.typography.headlineLarge,
                                                fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                                color = Color(CATEGORY_GREEN_COLOR),
                                                modifier = Modifier.padding(vertical = 8.dp)
                                            )
                                            Text(
                                                text = "CUSTOM OPTIONS",
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = Color.Gray,
                                                modifier = Modifier.padding(bottom = 16.dp)
                                            )
                                            listOptionSet.get(index).let {
                                                OptionsUICondition2(
                                                    optionSet = it,
                                                    onOptionSelected = { selectedOption, isChecked, optionSet ->
                                                        if (selectedOption != null) {
                                                            if (isChecked) {
                                                                viewModel.addSelectedOptionCondition2(
                                                                    optionDetails = optionDetails()
                                                                        ?: OptionDetails(),
                                                                    option = selectedOption,
                                                                    currentOptionSet = optionSet,
                                                                    stockItem = storeItem,
                                                                    stock = stock,
                                                                    state = state
                                                                )
                                                            } else {
                                                                viewModel.removeSelectedOptionCondition2(
                                                                    optionDetails = optionDetails()
                                                                        ?: OptionDetails(),
                                                                    selectedOption,
                                                                    currentOptionSet = optionSet,
                                                                    stockItem = storeItem,
                                                                    stock = stock,
                                                                    state = state
                                                                )
                                                            }
                                                        }
                                                    },
                                                    viewModel = viewModel,
                                                    onUpdateStock = { option, optionSet, optionStock ->
                                                        viewModel.updateOptionStock(
                                                            option = option ?: Option(),
                                                            currentOptionSet = optionSet,
                                                            stock = stock,
                                                            optionStock = optionStock,
                                                            optionDetails = optionDetails()
                                                                ?: OptionDetails(),
                                                            stockItem = storeItem
                                                        )
                                                    })
                                            }
                                        }
                                    }
                                }

                            }

                            // Total Section
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 16.dp),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "TOTAL",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                    color = Color.Black,
                                    modifier = Modifier.weight(0.3f)
                                )

                                // Total Stock Counter
                                Row(
                                    modifier = Modifier
                                        .width(170.dp)
                                        .background(
                                            Color(CATEGORY_GREEN_COLOR),
                                            RoundedCornerShape(16.dp)
                                        ),
                                    horizontalArrangement = Arrangement.Center,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    IconButton(
                                        onClick = {
                                            if (stock > 1) {
                                                updateStock(
                                                    stock - 1,
                                                    optionDetails() ?: OptionDetails(),
                                                    storeItem
                                                )
                                            }
                                        }
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Remove,
                                            contentDescription = "Decrease",
                                            tint = Color.White,
                                            modifier = Modifier
                                                .padding(8.dp)
                                                .size(24.dp)
                                        )
                                    }

                                    Text(
                                        text = "$stock",
                                        color = Color.Black,
                                        style = MaterialTheme.typography.headlineSmall,
                                        modifier = Modifier
                                            .background(
                                                color = Color(0xFFCCCCCC)
                                            )
                                            .padding(vertical = 16.dp, horizontal = 24.dp)
                                    )

                                    IconButton(
                                        onClick = {
                                            updateStock(
                                                stock + 1,
                                                optionDetails() ?: OptionDetails(),
                                                storeItem
                                            )
                                        }
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Add,
                                            contentDescription = "Increase",
                                            tint = Color.White,
                                            modifier = Modifier
                                                .padding(8.dp)
                                                .size(24.dp)
                                        )
                                    }
                                }

                                Text(
                                    text = "£${productTotal.transformDecimal()}",
                                    style = MaterialTheme.typography.headlineSmall,
                                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                    color = Color.Black,
                                    modifier = Modifier.weight(0.3f),
                                    textAlign = androidx.compose.ui.text.style.TextAlign.End
                                )
                            }

                            // Buttons Row
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 24.dp),
                                horizontalArrangement = Arrangement.spacedBy(16.dp)
                            ) {
                                // Cancel Button
                                Button(
                                    modifier = Modifier
                                        .weight(1f)
                                        .height(60.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color.LightGray,
                                        contentColor = Color.Black
                                    ),
                                    onClick = onDismiss,
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    Text(
                                        text = "Cancel",
                                        style = MaterialTheme.typography.headlineSmall,
                                        fontWeight = androidx.compose.ui.text.font.FontWeight.Bold
                                    )
                                }

                                val isEditing =
                                    state.getCurrentTableOrder().carts?.any { it.uuid == appliedPrefill?.uuid }
                                        ?: false
                                val buttonLabel = if (isEditing) "Update Cart Item" else "Add to Cart"

                                Button(
                                    modifier = Modifier
                                        .weight(1f)
                                        .height(60.dp),
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = if (areRequiredOptionsSelected) Color(
                                            CATEGORY_GREEN_COLOR
                                        ) else Color.Gray,
                                        contentColor = Color.White,
                                        disabledContainerColor = Color.Gray,
                                        disabledContentColor = Color.White
                                    ),
                                    enabled = areRequiredOptionsSelected,
                                    onClick = {
                                        if (areRequiredOptionsSelected) {
                                            addToCart(
                                                storeItem,
                                                optionDetails() ?: OptionDetails(),
                                                appliedPrefill?.copy(
                                                    storeItem = storeItem.copy(
                                                        optionSets = optionDetails()?.optionSets
                                                            ?: mutableListOf()
                                                    )
                                                ),
                                                isEditing
                                            )
                                        }
                                    },
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    val buttonText = when {
                                        areRequiredOptionsSelected -> buttonLabel
                                        unselectedRequiredOptionSets.size == 1 -> "Select ${unselectedRequiredOptionSets.first().name}"
                                        unselectedRequiredOptionSets.size > 1 -> "Select Required Options"
                                        else -> "Select Required Options"
                                    }

                                    Text(
                                        text = buttonText,
                                        style = MaterialTheme.typography.headlineSmall,
                                        fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                                        color = Color.White
                                    )
                                }
                            }
                        }

                    }
                }
            }
        }
    }
}

@Composable
private fun LazyListScope.ProductImageAndDesc(stockItem: StockItem) {

    Spacer(modifier = Modifier.height(16.dp))

    val imageUrl = "$BASE_DOMAIN/dasa/streamer?name=${stockItem.pic}"

    val request: ImageRequest =
        ImageRequest.Builder(LocalContext.current.applicationContext).data(imageUrl).crossfade(true)
            .diskCacheKey(imageUrl).diskCachePolicy(CachePolicy.ENABLED)
            .setHeader("Cache-Control", "max-age=31536000").build()

    // Image
    AsyncImage(
        model = request,
        contentDescription = stockItem.name,
        modifier = Modifier
            .height(120.sdp)
            .clip(RoundedCornerShape(7.sdp))
            .fillMaxWidth(),
        contentScale = ContentScale.Crop
    )
    Spacer(modifier = Modifier.height(8.dp))
    // Title
    Text(
        text = stockItem.name ?: "",
        style = MaterialTheme.typography.headlineSmall,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis
    )
    Spacer(modifier = Modifier.height(4.dp))
    // Description
    /*if(false) {
        Text(
            text = stockItem.description ?: "",
            style = MaterialTheme.typography.bodySmall,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.height(8.dp))
    }*/
    Text(
        text = "Price: £${stockItem.price?.transformDecimal() ?: "0.00"}",
        style = MaterialTheme.typography.headlineSmall,
        maxLines = 2,
        overflow = TextOverflow.Ellipsis
    )
    Spacer(modifier = Modifier.height(8.dp))
}


@Composable
fun StockUpdateCounter(
    initialStock: Int = 1, onStockChange: (Int) -> Unit
) {
    var stock by remember { mutableStateOf(initialStock) }

    Row(
        modifier = Modifier
            .padding(8.dp)
            .wrapContentWidth()
            .background(color = Color.LightGray, shape = RoundedCornerShape(20.sdp)),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        IconButton(onClick = {
            if (stock > 1) {
                stock--
                onStockChange(stock)
            }
        }) {
            Icon(imageVector = Icons.Default.RemoveCircle, contentDescription = "Decrease")
        }

        Text(text = stock.toString(), style = MaterialTheme.typography.bodyLarge)

        IconButton(onClick = {
            stock++
            onStockChange(stock)
        }) {
            Icon(imageVector = Icons.Default.AddCircle, contentDescription = "Increase")
        }
    }
}

@Composable
fun OptionStockUpdateCounter(
    option: Option, onStockChange: (Int, Option) -> Unit, modifier: Modifier = Modifier
) {
    var stock = option.quantity ?: 0

    Row(
        modifier = modifier
            .padding(8.dp)
            .wrapContentWidth()
            .background(color = Color.LightGray, shape = RoundedCornerShape(20.sdp)),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        IconButton(onClick = {
            if (stock > 0) {
                stock--
                onStockChange(stock, option)
            } else {
                onStockChange(stock, option)
            }
        }) {
            Icon(imageVector = Icons.Default.RemoveCircle, contentDescription = "Decrease")
        }

        Text(text = stock.toString(), style = MaterialTheme.typography.bodyMedium)

        IconButton(onClick = {
            stock++
            onStockChange(stock, option)
        }) {
            Icon(imageVector = Icons.Default.AddCircle, contentDescription = "Increase")
        }
    }
}

@Composable
fun OptionsUICondition0(
    viewModel: ProductsScreenViewModel,
    optionSet: OptionSet,
    onOptionSelected: (Option?, Boolean, OptionSet) -> Unit,
    onUpdateStock: (Option?, OptionSet, Int) -> Unit
) {

    Column(modifier = Modifier.padding(0.dp)) {
        optionSet.options.forEach { option ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Column 1: Option Name
                Text(
                    text = option?.name ?: "",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.weight(0.4f)
                )

                // Column 2: Stock Counter
                Row(
                    modifier = Modifier
                        .width(170.dp)
                        .background(
                            Color(CATEGORY_GREEN_COLOR),
                            RoundedCornerShape(16.dp)
                        ),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = {
                            val currentStock = option?.quantity ?: 0
                            if (currentStock > 0) {
                                onUpdateStock(option, optionSet, currentStock - 1)
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Remove,
                            contentDescription = "Decrease",
                            tint = Color.White,
                            modifier = Modifier
                                .padding(8.dp)
                                .size(24.dp)
                        )
                    }

                    Text(
                        text = "${option?.quantity ?: 1}",
                        color = Color.Black,
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier
                            .background(
                                color = Color(0xFFCCCCCC)
                            )
                            .padding(vertical = 16.dp, horizontal = 24.dp)
                    )

                    IconButton(
                        onClick = {
                            val currentStock = option?.quantity ?: 0
                            onUpdateStock(option, optionSet, currentStock + 1)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Increase",
                            tint = Color.White,
                            modifier = Modifier
                                .padding(8.dp)
                                .size(24.dp)
                        )
                    }
                }

                // Column 3: Price
                Text(
                    text = "£${option?.price?.transformDecimal() ?: "0.00"}",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.weight(0.3f),
                    textAlign = androidx.compose.ui.text.style.TextAlign.End
                )
            }
        }
    }
}

@Composable
fun OptionsUICondition1(
    optionSet: OptionSet,
    viewModel: ProductsScreenViewModel,
    onOptionSelected: (Option?, OptionSet) -> Unit
) {
    Column(modifier = Modifier.padding(0.dp)) {
        optionSet.options.forEach { option ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Column 1: Option Name (Left)
                Text(
                    text = option?.name ?: "",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.weight(0.4f)
                )

                // Column 2: Radio Button (Center)
                RadioButton(
                    selected = option?.optionchecked == true,
                    onClick = { onOptionSelected(option, optionSet) },
                    modifier = Modifier.weight(0.3f)
                )

                // Column 3: Price (Right, aligned)
                Text(
                    text = "£${option?.price?.transformDecimal() ?: "0.00"}",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.weight(0.3f),
                    textAlign = androidx.compose.ui.text.style.TextAlign.End
                )
            }
        }
    }
}

@Composable
fun OptionsUICondition2(
    viewModel: ProductsScreenViewModel,
    optionSet: OptionSet,
    onOptionSelected: (Option?, Boolean, OptionSet) -> Unit,
    onUpdateStock: (Option?, OptionSet, Int) -> Unit
) {

    Column(modifier = Modifier.padding(0.dp)) {
        optionSet.options.forEach { option ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Column 1: Option Name
                Text(
                    text = option?.name ?: "",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.weight(0.4f)
                )

                // Column 2: Stock Counter
                Row(
                    modifier = Modifier
                        .width(170.dp)
                        .background(
                            Color(CATEGORY_GREEN_COLOR),
                            RoundedCornerShape(16.dp)
                        ),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = {
                            val currentStock = option?.quantity ?: 0
                            if (currentStock > 0) {
                                onUpdateStock(option, optionSet, currentStock - 1)
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Remove,
                            contentDescription = "Decrease",
                            tint = Color.White,
                            modifier = Modifier
                                .padding(8.dp)
                                .size(24.dp)
                        )
                    }

                    Text(
                        text = "${option?.quantity ?: 1}",
                        color = Color.Black,
                        style = MaterialTheme.typography.headlineSmall,
                        modifier = Modifier
                            .background(
                                color = Color(0xFFCCCCCC)
                            )
                            .padding(vertical = 16.dp, horizontal = 24.dp)
                    )

                    IconButton(
                        onClick = {
                            val currentStock = option?.quantity ?: 0
                            onUpdateStock(option, optionSet, currentStock + 1)
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Add,
                            contentDescription = "Increase",
                            tint = Color.White,
                            modifier = Modifier
                                .padding(8.dp)
                                .size(24.dp)
                        )
                    }
                }

                // Column 3: Price
                Text(
                    text = "£${option?.price?.transformDecimal() ?: "0.00"}",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = androidx.compose.ui.text.font.FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.weight(0.3f),
                    textAlign = androidx.compose.ui.text.style.TextAlign.End
                )
            }
        }
    }
}
