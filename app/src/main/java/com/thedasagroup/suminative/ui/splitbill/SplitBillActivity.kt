package com.thedasagroup.suminative.ui.splitbill

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Payment
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.payment.PaymentDialogHelper
import com.thedasagroup.suminative.ui.payment.PaymentState
import com.thedasagroup.suminative.ui.payment.PaymentViewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal

class SplitBillActivity : AppCompatActivity(), MavericksView {
    val paymentViewModel: PaymentViewModel by viewModel()

    companion object {
        private const val EXTRA_ORDER = "extra_order"
        private const val EXTRA_NUMBER_OF_PERSONS = "extra_number_of_persons"

        fun createIntent(context: Context, order: Order, numberOfPersons: Int): Intent {
            return Intent(context, SplitBillActivity::class.java).apply {
                putExtra(EXTRA_ORDER, order)
                putExtra(EXTRA_NUMBER_OF_PERSONS, numberOfPersons)
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val order = intent.getParcelableExtra<Order>(EXTRA_ORDER) ?: Order()
        val numberOfPersons = intent.getIntExtra(EXTRA_NUMBER_OF_PERSONS, 1)

        setContent {
            SumiNativeTheme {
                SplitBillScreen(
                    order = order,
                    numberOfPersons = numberOfPersons,
                    onBackClick = { finish() },
                    onPaymentClick = { personIndex, amount ->
                        // Create a new order for this person's portion
                        val splitOrder = order.copy(
                            totalPrice = amount,
                            netPayable = amount - (order.tax ?: 0.0)
                        )
                        
                        // Show payment dialog for this person
                        PaymentDialogHelper.showPaymentDialog(
                            activity = this@SplitBillActivity,
                            order = splitOrder,
                            onPaymentSuccess = { completedOrder ->
                                paymentViewModel.updatePaymentsMap(personNumber = personIndex, isPaid = true)
                            }
                        )
                    },
                    paymentViewModel = paymentViewModel
                )
            }
        }
    }

    override fun invalidate() {

    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SplitBillScreen(
    order: Order,
    numberOfPersons: Int,
    onBackClick: () -> Unit,
    onPaymentClick: (Int, Double) -> Unit,
    paymentViewModel: PaymentViewModel
) {
    val totalAmount = order.totalPrice ?: 0.0
    val amountPerPerson = totalAmount / numberOfPersons
    val paymentsMap by paymentViewModel.collectAsState(PaymentState::paymentsMap)

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Split Bill Payment",
                        fontFamily = fontPoppins,
                        fontWeight = FontWeight.Bold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back",
                            tint = Color(0xFF2E7D32)
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White,
                    titleContentColor = Color(0xFF2E7D32)
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(Color(0xFFF5F5F5))
        ) {
            // Summary Card
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(containerColor = Color.White),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                shape = RoundedCornerShape(12.dp)
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Bill Summary",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E7D32),
                        fontFamily = fontPoppins
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Total Amount:",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins
                        )
                        Text(
                            text = "£${totalAmount.transformDecimal()}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2E7D32),
                            fontFamily = fontPoppins
                        )
                    }
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Split between:",
                            fontSize = 16.sp,
                            fontFamily = fontPoppins
                        )
                        Text(
                            text = "$numberOfPersons persons",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2E7D32),
                            fontFamily = fontPoppins
                        )
                    }
                    
                    Divider(
                        modifier = Modifier.padding(vertical = 12.dp),
                        color = Color.Black.copy(alpha = 0.2f)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "Amount per person:",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            fontFamily = fontPoppins
                        )
                        Text(
                            text = "£${amountPerPerson.transformDecimal()}",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2E7D32),
                            fontFamily = fontPoppins
                        )
                }
            }

            // Payment List
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                items(numberOfPersons) { index ->
                    PersonPaymentCard(
                        personNumber = index + 1,
                        amount = amountPerPerson,
                        isPaid = paymentsMap[index] == true,
                        onPaymentClick = {
                            onPaymentClick(index, amountPerPerson)
                        }
                    )
                }
            }
        }
    }
}
}

@Composable
fun PersonPaymentCard(
    personNumber: Int,
    amount: Double,
    isPaid: Boolean,
    onPaymentClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isPaid) Color(0xFFE8F5E8) else Color.White
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Person $personNumber",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    fontFamily = fontPoppins
                )
                Text(
                    text = "£${amount.transformDecimal()}",
                    fontSize = 16.sp,
                    color = Color(0xFF2E7D32),
                    fontWeight = FontWeight.Medium,
                    fontFamily = fontPoppins
                )
            }

            if (isPaid) {
                Text(
                    text = "PAID",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0xFF2E7D32),
                    fontFamily = fontPoppins,
                    modifier = Modifier
                        .background(
                            Color(0xFF2E7D32).copy(alpha = 0.1f),
                            RoundedCornerShape(8.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 6.dp)
                )
            } else {
                Button(
                    onClick = onPaymentClick,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF2E7D32),
                        contentColor = Color.White
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Payment,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Pay Now",
                        fontWeight = FontWeight.Bold,
                        fontFamily = fontPoppins
                    )
                }
            }
        }
    }
}
