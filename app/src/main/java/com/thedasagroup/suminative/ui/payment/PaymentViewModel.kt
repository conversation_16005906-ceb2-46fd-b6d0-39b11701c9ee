package com.thedasagroup.suminative.ui.payment

import android.util.Log
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.data.model.response.my_guava.sessions.Session
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.GetTerminalListResponse
import com.thedasagroup.suminative.data.model.response.my_guava.terminals.Terminal
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.domain.myguava.MyGuavaCheckStatusUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateOrderUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaCreateSessionUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetTerminalsUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakePaymentUseCase
import com.thedasagroup.suminative.domain.myguava.MyGuavaMakeRefundUseCase
import com.thedasagroup.suminative.ui.products.PlaceOnlineOrderUseCase
import com.thedasagroup.suminative.ui.products.OrderUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.isActive
import java.util.UUID

class PaymentViewModel @AssistedInject constructor(
    @Assisted initialState: PaymentState,
    val myGuavaCreateOrderUseCase: MyGuavaCreateOrderUseCase,
    val getTerminalsUseCase: MyGuavaGetTerminalsUseCase,
    val createSessionUseCase: MyGuavaCreateSessionUseCase,
    val makePaymentUseCase: MyGuavaMakePaymentUseCase,
    val makeRefundUseCase: MyGuavaMakeRefundUseCase,
    val checkStatusUseCase: MyGuavaCheckStatusUseCase,
    val orderUseCase: OrderUseCase,
    val onlineOrderUseCase: PlaceOnlineOrderUseCase,
    val guavaRepository: MyGuavaRepository,
    val prefs: Prefs
) : MavericksViewModel<PaymentState>(initialState) {

    var loopJob: Job? = null

    init {
        viewModelScope.launch {
            getTerminalList()
        }
    }


    suspend fun getTerminalList(): StateFlow<Async<GetTerminalListResponse>> {
        val flow = MutableStateFlow<Async<GetTerminalListResponse>>(Loading())
        setState {
            copy(terminalListResponse = Loading())
        }
        getTerminalsUseCase().execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    val terminals = it()()?.data?.list
                    val firstAvailableTerminal =
                        terminals?.firstOrNull { terminal -> terminal.available == true }
                            ?: terminals?.firstOrNull()
                    copy(
                        terminalListResponse = it(),
                        selectedTerminal = firstAvailableTerminal
                    )
                }

                else -> {
                    flow.value = Uninitialized
                    copy(terminalListResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun createSession(
        terminal: Terminal,
        guavaOrder: GuavaOrder
    ): StateFlow<Async<Session>> {
        val flow = MutableStateFlow<Async<Session>>(Loading())
        setState {
            copy(createSessionResponse = Loading())
        }
        createSessionUseCase(terminal = terminal, guavaOrder = guavaOrder).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(createSessionResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(createSessionResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun makePayment(order: Order, terminal: Terminal): StateFlow<Async<Session>> {
        val flow = MutableStateFlow<Async<Session>>(Loading())
        loopJob?.cancel()
        setState {
            copy(
                makePaymentResponse = Loading(), createSessionResponse = Uninitialized,
                checkStatusLoopResponse = Uninitialized
            )
        }
        makePaymentUseCase(order = order, terminal = terminal).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    setState {
                        copy(checkStatusLoopResponse = Loading())
                    }
                    startCheckingPaymentStatus(
                        sessionId = it()()?.id ?: "",
                        onSuccess = {
                            viewModelScope.launch {
                                val orderResponseFlow = onlineOrderUseCase(order = order, order.transactionId ?: UUID.randomUUID().toString())
                                when (orderResponseFlow.value) {
                                    is Success -> {
                                        setState {
                                            copy(
                                                checkStatusLoopResponse = Success(Unit),
                                                paymentOrderResponse = Success(
                                                    orderResponseFlow.value() ?: OrderResponse2()
                                                )
                                            )
                                        }
                                    }

                                    is Fail -> {
                                        setState {
                                            copy(
                                                checkStatusLoopResponse = Fail(
                                                    Throwable(
                                                        (orderResponseFlow.value as Fail<OrderResponse2>).error.message
                                                            ?: ""
                                                    )
                                                )
                                            )
                                        }
                                    }

                                    else -> {

                                    }
                                }
                            }
                        },
                        onFailure = { errorMessage ->
                            setState {
                                copy(checkStatusLoopResponse = Fail(Throwable(errorMessage)))
                            }
                        }
                    )
                    copy(makePaymentResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(makePaymentResponse = Uninitialized)
                }
            }
        }
        return flow
    }


    suspend fun makeRefund(terminal: Terminal, guavaOrder: GuavaOrder): StateFlow<Async<Session>> {
        val flow = MutableStateFlow<Async<Session>>(Loading())
        loopJob?.cancel()
        setState {
            copy(
                makePaymentResponse = Loading(), createSessionResponse = Uninitialized,
                checkStatusLoopResponse = Uninitialized
            )
        }
        makeRefundUseCase(guavaOrder = guavaOrder, terminal = terminal).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    setState {
                        copy(checkStatusLoopResponse = Loading())
                    }
                    startCheckingPaymentStatus(
                        sessionId = it()()?.id ?: "",
                        onSuccess = {
                            setState {
                                copy(checkStatusLoopResponse = Success(Unit))
                            }
                        },
                        onFailure = { errorMessage ->
                            setState {
                                copy(checkStatusLoopResponse = Fail(Throwable(errorMessage)))
                            }
                        }
                    )
                    copy(makePaymentResponse = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(makePaymentResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    suspend fun checkStatus(sessionId: String): StateFlow<Async<Session>> {
        val flow = MutableStateFlow<Async<Session>>(Loading())
        setState {
            copy(checkStatusSession = Loading())
        }
        checkStatusUseCase(sessionId = sessionId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(checkStatusSession = it())
                }

                else -> {
                    flow.value = Uninitialized
                    copy(checkStatusSession = Uninitialized)
                }
            }
        }
        return flow
    }

    fun updateOrder(order: Order) {
        setState {
            copy(order = Success(order))
        }
    }

    fun startCheckingPaymentStatus(
        sessionId: String,
        onSuccess: () -> Unit,
        onFailure: (String) -> Unit
    ) {
        loopJob?.cancel()
        loopJob = viewModelScope.launch(Dispatchers.IO) {
            val endTime = System.currentTimeMillis() + (5 * 60 * 1000) // 5 minutes
            var success = false

            while (System.currentTimeMillis() < endTime && !success && isActive) {
                try {
                    val statflow = checkStatusUseCase(sessionId = sessionId)
                    val result = statflow.value
                    when (result) {
                        is Success -> {
                            when (result().status) {
                                "AUTHORIZED" -> {
                                    success = true
                                    onSuccess()
                                    break
                                }

                                "CREATED", "PENDING" -> {
                                    delay(3000)
                                }

                                else -> {
                                    success = true
                                    onFailure(result().messageForUser ?: "Unknown Error")
                                    break
                                }
                            }
                        }

                        is Fail -> {
                            onFailure(result.error.message ?: "")
                            break
                        }

                        else -> {
                            delay(3000)
                        }
                    }
                } catch (e: Exception) {
                    Log.e("PaymentViewModel", "Error checking payment status", e)
                    delay(3000)
                }
            }

            if (!success && isActive) {
                onFailure("Payment verification timed out after 5 minutes")
            }

            loopJob = null
        }
    }

    fun cancelPayment(sessionId: String?) {
        // Cancel status checking job
        loopJob?.cancel()
        loopJob = null

        // Set canceling state to Loading
        setState {
            copy(cancelingPayment = Loading())
        }

        // If we have an active session ID, cancel it on the server
        viewModelScope.launch {
            try {
                if (sessionId != null) {
                    guavaRepository.cancelSession(sessionId).collect { result ->
                        // When we get a result, update the state
                        when (result) {
                            is Success -> {
                                setState {
                                    copy(
                                        makePaymentResponse = Uninitialized,
                                        checkStatusSession = Uninitialized,
                                        checkStatusLoopResponse = Uninitialized,
                                        cancelingPayment = Success(Unit)
                                    )
                                }
                            }

                            is Fail -> {
                                setState {
                                    copy(cancelingPayment = Fail(result.error))
                                }
                            }

                            else -> {
                                // Keep Loading state
                            }
                        }
                    }
                } else {
                    // If there's no session to cancel, just reset states
                    setState {
                        copy(
                            makePaymentResponse = Uninitialized,
                            checkStatusSession = Uninitialized,
                            checkStatusLoopResponse = Uninitialized,
                            cancelingPayment = Success(Unit)
                        )
                    }
                }
            } catch (e: Exception) {
                // In case of error, update state
                setState {
                    copy(cancelingPayment = Fail(Throwable("Failed to cancel payment: ${e.message}")))
                }
            }
        }
    }

    fun selectTerminal(terminal: Terminal) {
        setState {
            copy(selectedTerminal = terminal)
        }
    }

    fun showChangeDialog(showChangeDialog: Boolean) {
        // Only show if not already showing
        setState {
            if (showChangeDialog) {
                copy(showChangeDialog = true)
            } else {
                copy(showChangeDialog = false)
            }
        }
    }

    fun hideChangeDialog() {
        setState {
            copy(showChangeDialog = false)
        }
    }

    fun updateAmountGivenText(amount: String) {
        setState {
            copy(amountGivenText = amount)
        }
    }

    fun showChangeCalculation(show: Boolean) {
        setState {
            copy(showChangeCalculation = show)
        }
    }

    fun updatePaymentsMap(personNumber: Int, isPaid: Boolean) {
        setState {
            copy(paymentsMap = paymentsMap + (personNumber to isPaid))
        }
    }

    fun isMyGuava(): Boolean {
        val storeId = prefs.store?.id
        return prefs.loginResponse?.stores?.firstOrNull { it.id == storeId }?.paymentProcessorName == "My Guava"
    }

    fun isSumUp(): Boolean {
        return false
    }

    override fun onCleared() {
        super.onCleared()
        loopJob?.cancel()
        loopJob = null
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<PaymentViewModel, PaymentState> {
        override fun create(state: PaymentState): PaymentViewModel
    }

    companion object :
        MavericksViewModelFactory<PaymentViewModel, PaymentState> by hiltMavericksViewModelFactory()
}

data class PaymentState(
    val order: Async<Order> = Uninitialized,
    val createOrderResponse: Async<GuavaOrderResponse> = Uninitialized,
    val terminalListResponse: Async<GetTerminalListResponse> = Uninitialized,
    val selectedTerminal: Terminal? = null,
    val createSessionResponse: Async<Session> = Uninitialized,
    val makePaymentResponse: Async<Session> = Uninitialized,
    val checkStatusSession: Async<Session> = Uninitialized,
    val checkStatusLoopResponse: Async<Unit> = Uninitialized,
    val cancelingPayment: Async<Unit> = Uninitialized,
    val showChangeDialog: Boolean = false,
    val paymentOrderResponse: Async<OrderResponse2> = Uninitialized,
    val amountGivenText : String = "",
    val showChangeCalculation : Boolean = false,
    val paymentsMap : Map<Int, Boolean> = mutableMapOf()
) : MavericksState