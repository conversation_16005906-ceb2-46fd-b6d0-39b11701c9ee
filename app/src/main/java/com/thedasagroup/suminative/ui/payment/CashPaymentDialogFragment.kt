package com.thedasagroup.suminative.ui.payment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.DialogFragment
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.mocking.MockableMavericksView
import com.thedasagroup.suminative.data.model.request.order.Order

class CashPaymentDialogFragment : DialogFragment(), MockableMavericksView {

    fun newInstance(order: Order, onPaymentComplete: (Order) -> Unit): CashPaymentDialogFragment {
        val args = Bundle()
        args.putParcelable(ARG_ORDER, order)
        val fragment = CashPaymentDialogFragment()
        fragment.onPaymentComplete = onPaymentComplete
        fragment.arguments = args
        return fragment
    }

    private val ARG_ORDER: String? = "extra_order"
    private var onPaymentComplete : ((Order) -> Unit)? = null

    val viewModel: PaymentViewModel by fragmentViewModel()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        super.onCreateView(inflater, container, savedInstanceState)
        return ComposeView(requireContext()).apply {
            setContent {
                CashPaymentDialogContent(
                    order = arguments?.getParcelable(ARG_ORDER) ?: Order(),
                    onDismiss = { dismiss() },
                    onPaymentComplete = { completedOrder ->
                        dismiss()
                        onPaymentComplete?.invoke(completedOrder)
                    },
                    viewModel = viewModel
                )
            }
        }
    }

    override fun invalidate() {

    }

    override fun provideMocks() = mocks()
}