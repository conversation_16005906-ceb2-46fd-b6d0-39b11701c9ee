package com.thedasagroup.suminative

import android.content.Context
import android.graphics.Bitmap
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Restaurant
import androidx.compose.material.icons.filled.TableRestaurant
import androidx.compose.ui.graphics.vector.ImageVector
import com.sunmi.printerx.enums.Align
import com.sunmi.printerx.style.BitmapStyle
import com.thedasagroup.suminative.ui.printer.selectPrinter
import com.thedasagroup.suminative.ui.utils.SunmiPrintHelper
import kotlinx.serialization.Serializable

abstract class BaseActivity : AppCompatActivity() {

}

fun printOrderBitmap(bitmap: Bitmap, context: Context) {
    if (BuildConfig.USES_PRINTER_X.isNotEmpty()) {
        selectPrinter?.lineApi()?.run {
            printBitmap(bitmap, BitmapStyle.getStyle().setAlign(Align.CENTER))
            autoOut()
        }
    } else {
        SunmiPrintHelper.getInstance().initSunmiPrinterService(context)
        SunmiPrintHelper.getInstance().initPrinter()
        SunmiPrintHelper.getInstance().feedPaper()
        SunmiPrintHelper.getInstance().printBitmap(bitmap, 0)
        SunmiPrintHelper.getInstance().feedPaper()
    }
}


data class TopLevelRoute<T : Any>(val name: String, val route: T, val icon: ImageVector)

val topLevelRoutes = listOf(
    TopLevelRoute("Store Items", StoreItems("StoreItems"), Icons.Default.Home),
    TopLevelRoute("Tables", Tables("Tables"), Icons.Default.TableRestaurant),
)
val topLevelRoutesReservations = listOf(
    TopLevelRoute("Store Items", StoreItems("StoreItems"), Icons.Default.Home),
    TopLevelRoute("Tables", Tables("Tables"), Icons.Default.TableRestaurant),
    TopLevelRoute("Reservations", Reservations("Reservations"), Icons.Default.Restaurant)
)

@Serializable
data class StoreItems(val id: String)

@Serializable
data class Tables(val id: String)

@Serializable
data class StockManagement(val id: String)

@Serializable
data class Reservations(val id: String)

@Serializable
data class RegularOrders(val id: String)